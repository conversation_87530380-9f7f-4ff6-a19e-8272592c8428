{"code": 0, "done": true, "data": {"account_name": "小哈学Java", "account_alias": null, "account_avatar": "http://wx.qlogo.cn/mmhead/Q3auHgzwzM6ic4X45AUnpXYUQkPs87V3XX2hTH9r0OK9hbLAD6j77dg/132", "account_description": "码龄9年，前某厂中台研发。专注于Java领域干货分享，不限于BAT面试, 算法，数据库，Spring Boot, 微服务,高并发, JVM, Docker容器，ELK相关知识，期待与您一同进步。", "account_id": "gh_66a5b32165b7", "account_biz": "MzU4MDUyMDQyNQ==", "account_biz_number": **********, "account_qr_code": "https://open.weixin.qq.com/qr/code?username=gh_66a5b32165b7", "msg_has_copyright": false, "msg_content": "<section data-tool=\"mdnice编辑器\" data-website=\"https://www.mdnice.com\" style=\"margin-top: 0px;margin-bottom: 0px;margin-left: 0px;margin-right: 0px;padding-top: 0px;padding-bottom: 0px;padding-left: 10px;padding-right: 10px;background-attachment: scroll;background-clip: border-box;background-color: rgba(0, 0, 0, 0);background-image: none;background-origin: padding-box;background-position-x: 0%;background-position-y: 0%;background-repeat: no-repeat;background-size: auto;width: auto;font-family: Optima-Regular, Optima, PingFangSC-light, PingFangTC-light, 'PingFang SC', Cambria, Cochin, Georgia, Times, 'Times New Roman', serif;font-size: 16px;color: rgb(0, 0, 0);line-height: 1.5em;word-spacing: 0em;letter-spacing: 0em;word-break: break-word;overflow-wrap: break-word;text-align: left;\" data-pm-slice=\"0 0 []\" data-mpa-action-id=\"m8o3osod1huk\"><section style=\"text-align: center;\" nodeleaf=\"\"><img class=\"rich_pages wxw-img\" data-imgfileid=\"100074536\" data-ratio=\"0.4255555555555556\" data-s=\"300,640\" data-src=\"https://mmbiz.qpic.cn/sz_mmbiz_jpg/knmrNHnmCLE2wTd9jX9u0GWe2XtE2gG0nuWk0hsZGVMZTicl4qa19MG92EdFAkNWTgL6YqKNSI0LJclbA8z2tvQ/640?wx_fmt=jpeg&amp;from=appmsg\" data-type=\"jpeg\" data-w=\"900\" type=\"block\"></section><p data-tool=\"mdnice编辑器\" style=\"color: rgb(63, 63, 63);font-size: 13px;line-height: 1.8em;letter-spacing: 0.02em;text-align: right;text-indent: 0em;margin: 0px;padding: 16px 0px 8px;\" mpa-font-style=\"m8o3n1wdt7y\" data-mpa-action-id=\"m8o3n1wk1zoc\" data-pm-slice=\"0 0 []\"><span leaf=\"\"><span textstyle=\"\" style=\"color: rgb(136, 136, 136);\">来源：阿里巴巴中间件</span></span></p><section data-tool=\"mdnice编辑器\" class=\"js_darkmode__1\" data-pm-slice=\"0 0 []\" style=\"-webkit-tap-highlight-color: transparent;margin: 20px 0px;padding: 10px 10px 10px 20px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;font-style: normal;font-variant-ligatures: normal;font-variant-caps: normal;font-weight: 400;orphans: 2;text-indent: 0px;text-transform: none;widows: 2;word-spacing: 0px;-webkit-text-stroke-width: 0px;white-space: normal;text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;font-family: Optima-Regular, Optima, PingFangSC-light, PingFangTC-light, &quot;PingFang SC&quot;, Cambria, Cochin, Georgia, Times, &quot;Times New Roman&quot;, serif;letter-spacing: normal;text-align: left;border-width: 3px;border-style: none none none solid;color: var(--weui-FG-1);font-size: 15px;border-color: rgba(0, 0, 0, 0.4) rgba(0, 0, 0, 0.4) rgba(0, 0, 0, 0.4) rgb(53, 179, 120);background: none 0% 0% / auto no-repeat scroll padding-box border-box rgb(251, 249, 253);border-radius: 0px;width: auto;height: auto;box-shadow: rgba(0, 0, 0, 0) 0px 0px 0px 0px;overflow: auto;visibility: visible;\"><p class=\"js_darkmode__2\" style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 16px 0px 8px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;clear: both;min-height: 1em;color: rgb(63, 63, 63);text-indent: 0em;font-size: 16px;line-height: 1.8em;letter-spacing: 0.02em;visibility: visible;\"><span leaf=\"\" style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;visibility: visible;\">👉 欢迎</span><a href=\"https://mp.weixin.qq.com/s?__biz=MzU4MDUyMDQyNQ==&amp;mid=2247538511&amp;idx=1&amp;sn=c2f80c1f6143af7bfcaa661951d7bf23&amp;chksm=fd5787c9ca200edf9b861f51dcdc6e15a575488ab6be0f5b721ea27047cf3316e07ea33a9a8d&amp;token=343952052&amp;lang=zh_CN&amp;scene=21#wechat_redirect\" data-linktype=\"2\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);margin: 0px;padding: 0px;outline: 0px;color: rgb(72, 179, 120);text-decoration: none;-webkit-user-drag: none;cursor: pointer;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;letter-spacing: 0.02em;text-indent: 0em;word-spacing: 0em;caret-color: transparent;font-weight: bold;border-style: none none solid;border-width: 1px;border-color: rgb(30, 107, 184) rgb(30, 107, 184) rgb(72, 179, 120);border-radius: 0px;visibility: visible;\"><span leaf=\"\" style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;visibility: visible;\">加入小哈的星球</span></a><span style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;letter-spacing: 0.02em;text-indent: 0em;word-spacing: 0em;caret-color: transparent;visibility: visible;\"><span leaf=\"\" style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;visibility: visible;\">，你将获得:&nbsp;</span></span><strong style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;text-indent: 0em;caret-color: transparent;color: rgb(255, 104, 39);font-size: 15px;letter-spacing: 0.51px;word-spacing: 0.8px;visibility: visible;\"><span leaf=\"\" style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;visibility: visible;\">专属的项目实战 / 1v1 提问 /&nbsp;</span><strong style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;letter-spacing: 0.51px;word-spacing: 0.8px;visibility: visible;\"><span leaf=\"\" style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;visibility: visible;\">Java 学习路线 /&nbsp;</span></strong><span leaf=\"\" style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;visibility: visible;\">学习打卡 / 每月赠书 / 社群讨论</span></strong></p><ul style=\"-webkit-tap-highlight-color: transparent;margin: 8px 0px;padding: 0px 0px 0px 25px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;width: 524.469px;color: rgb(0, 0, 0);visibility: visible;\" class=\"list-paddingleft-1\"><li style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;visibility: visible;\"><section class=\"js_darkmode__4\" style=\"-webkit-tap-highlight-color: transparent;margin: 5px 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;color: rgb(63, 63, 63);font-size: 16px;line-height: 1.8em;letter-spacing: 0.02em;visibility: visible;\"><span style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;font-size: 15px;visibility: visible;\"><span leaf=\"\" style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;visibility: visible;\">新项目:</span><strong class=\"js_darkmode__5\" style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;border-color: rgba(0, 0, 0, 0.4);color: rgb(74, 74, 74);background: none 0% 0% / auto no-repeat scroll padding-box border-box rgba(0, 0, 0, 0);width: auto;height: auto;border-style: none;border-width: 3px;border-radius: 0px;visibility: visible;\"><span leaf=\"\" style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;visibility: visible;\">《从零手撸：仿小红书（微服务架构）》</span></strong><span leaf=\"\" style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;visibility: visible;\">&nbsp;正在持续爆肝中，基于 Spring Cloud Alibaba + Spring Boot 3.x + JDK 17...,&nbsp;</span></span><a href=\"https://mp.weixin.qq.com/s?__biz=MzU4MDUyMDQyNQ==&amp;mid=2247538491&amp;idx=1&amp;sn=576995017721766d0fe15723fd135619&amp;chksm=fd5787bdca200eab54d2fb8ca07fcc2bffdec3eaab4ab82ab5eaf949f0254c1683455e02010b&amp;token=343952052&amp;lang=zh_CN&amp;scene=21#wechat_redirect\" data-linktype=\"2\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);margin: 0px;padding: 0px;outline: 0px;color: rgb(72, 179, 120);text-decoration: none;-webkit-user-drag: none;cursor: pointer;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;letter-spacing: 0.02em;text-indent: 0em;word-spacing: 0em;font-weight: bold;border-style: none none solid;border-width: 1px;border-color: rgb(30, 107, 184) rgb(30, 107, 184) rgb(72, 179, 120);border-radius: 0px;visibility: visible;font-size: 15px;\"><span style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;visibility: visible;\"><span leaf=\"\" style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;visibility: visible;\">点击查看项目介绍</span></span></a><span style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;font-size: 15px;visibility: visible;\"><span leaf=\"\" style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;visibility: visible;\">；</span></span></section></li><li style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;visibility: visible;\"><section class=\"js_darkmode__6\" style=\"-webkit-tap-highlight-color: transparent;margin: 5px 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;color: rgb(63, 63, 63);font-size: 16px;line-height: 1.8em;letter-spacing: 0.02em;visibility: visible;\"><span style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;font-size: 15px;visibility: visible;\"><strong class=\"js_darkmode__7\" style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;border-color: rgba(0, 0, 0, 0.4);color: rgb(74, 74, 74);background: none 0% 0% / auto no-repeat scroll padding-box border-box rgba(0, 0, 0, 0);width: auto;height: auto;border-style: none;border-width: 3px;border-radius: 0px;visibility: visible;\"><span leaf=\"\" style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;visibility: visible;\">《从零手撸：前后端分离博客项目（全栈开发）》</span></strong><span leaf=\"\" style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;visibility: visible;\">&nbsp;2期已完结,演示链接：</span><span class=\"js_darkmode__8\" style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;color: rgb(0, 122, 170);letter-spacing: 0.544px;word-spacing: 0.8px;text-decoration: underline;visibility: visible;\"><span leaf=\"\" style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;visibility: visible;\">http://116.62.199.48/</span></span><span leaf=\"\" style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;visibility: visible;\">;</span></span></section></li><li style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;visibility: visible;\"><section class=\"js_darkmode__6\" style=\"-webkit-tap-highlight-color: transparent;margin: 5px 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;color: rgb(63, 63, 63);font-size: 16px;line-height: 1.8em;letter-spacing: 0.02em;visibility: visible;\"><span style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;font-size: 15px;visibility: visible;\"><span leaf=\"\" style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;visibility: visible;\">专栏阅读地址：</span><span style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;text-decoration: underline;color: rgb(0, 122, 170);visibility: visible;\"><span leaf=\"\" style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;visibility: visible;\">https://www.quanxiaoha.com/column</span></span></span><span leaf=\"\" style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;visibility: visible;\"><br></span></section></li></ul><p class=\"js_darkmode__9\" style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 16px 0px 8px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;clear: both;min-height: 1em;color: rgb(63, 63, 63);text-indent: 0em;font-size: 16px;line-height: 1.8em;letter-spacing: 0.02em;visibility: visible;\"><span style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;font-size: 15px;visibility: visible;\"><span leaf=\"\" style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;visibility: visible;\">截止目前，</span><strong style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;visibility: visible;\"><span leaf=\"\" style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;visibility: visible;\">累计输出 85w+ 字，讲解图 3088+ 张，还在持续爆肝中..</span></strong><span leaf=\"\" style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;visibility: visible;\">&nbsp;后续还会上新更多项目，目标是将 Java 领域典型的项目都整一波，如秒杀系统, 在线商城, IM 即时通讯，Spring Cloud Alibaba 等等，</span></span><a href=\"https://mp.weixin.qq.com/s?__biz=MzU4MDUyMDQyNQ==&amp;mid=2247538511&amp;idx=1&amp;sn=c2f80c1f6143af7bfcaa661951d7bf23&amp;chksm=fd5787c9ca200edf9b861f51dcdc6e15a575488ab6be0f5b721ea27047cf3316e07ea33a9a8d&amp;token=343952052&amp;lang=zh_CN&amp;scene=21#wechat_redirect\" data-linktype=\"2\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);margin: 0px;padding: 0px;outline: 0px;color: rgb(72, 179, 120);text-decoration: none;-webkit-user-drag: none;cursor: pointer;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;letter-spacing: 0.02em;text-indent: 0em;word-spacing: 0em;font-weight: bold;border-style: none none solid;border-width: 1px;border-color: rgb(30, 107, 184) rgb(30, 107, 184) rgb(72, 179, 120);border-radius: 0px;visibility: visible;font-size: 15px;\"><span style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;visibility: visible;\"><span leaf=\"\" style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;visibility: visible;\">戳我加入学习，解锁全部项目，已有3000+小伙伴加入</span></span></a></p><span leaf=\"\" style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;visibility: visible;\"><img data-src=\"https://mmbiz.qpic.cn/sz_mmbiz_gif/knmrNHnmCLFJRHck1SBQN2jMyV6c8uyy9cMShUicdfia3ibdNhX9qu5NpRlCYicDIoVMHMddwNuzI6NUSKnkHfL8Lg/640?wx_fmt=gif&amp;from=appmsg&amp;wxfrom=5&amp;wx_lazy=1&amp;wx_co=1&amp;tp=webp\" alt=\"图片\" class=\"rich_pages wxw-img __bg_gif\" data-ratio=\"0.4782205746061168\" data-type=\"gif\" data-w=\"1079\" style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;vertical-align: bottom;height: auto !important;color: rgb(106, 115, 125);font-size: 14.4px;letter-spacing: 0.544px;text-align: center;word-spacing: 0.8px;border-radius: 8px;background-size: 16px !important;visibility: visible !important;width: 594px !important;\" data-imgfileid=\"100073709\"></span></section><ul style=\"list-style-type: disc;margin-top: 8px;margin-bottom: 8px;margin-left: 0px;margin-right: 0px;padding-top: 0px;padding-bottom: 0px;padding-left: 25px;padding-right: 0px;color: rgb(0, 0, 0);\" class=\"list-paddingleft-1\"><li><section style=\"margin-top: 5px;margin-bottom: 5px;color: rgb(63, 63, 63);font-size: 16px;line-height: 1.8em;letter-spacing: 0.02em;text-align: left;font-weight: normal;\"><span leaf=\"\">先厘清一些基础概念</span></section></li><ul style=\"list-style-type: disc;margin-top: 8px;margin-bottom: 8px;margin-left: 0px;margin-right: 0px;padding-top: 0px;padding-bottom: 0px;padding-left: 25px;padding-right: 0px;color: rgb(0, 0, 0);\" class=\"list-paddingleft-1\"><li><section style=\"margin-top: 5px;margin-bottom: 5px;color: rgb(63, 63, 63);font-size: 16px;line-height: 1.8em;letter-spacing: 0.02em;text-align: left;font-weight: normal;\"><span leaf=\"\">1、什么是架构</span></section></li><li><section style=\"margin-top: 5px;margin-bottom: 5px;color: rgb(63, 63, 63);font-size: 16px;line-height: 1.8em;letter-spacing: 0.02em;text-align: left;font-weight: normal;\"><span leaf=\"\">2、什么是架构图</span></section></li><li><section style=\"margin-top: 5px;margin-bottom: 5px;color: rgb(63, 63, 63);font-size: 16px;line-height: 1.8em;letter-spacing: 0.02em;text-align: left;font-weight: normal;\"><span leaf=\"\">3、架构图的作用</span></section></li><li><section style=\"margin-top: 5px;margin-bottom: 5px;color: rgb(63, 63, 63);font-size: 16px;line-height: 1.8em;letter-spacing: 0.02em;text-align: left;font-weight: normal;\"><span leaf=\"\">4、架构图分类</span></section></li></ul><li><section style=\"margin-top: 5px;margin-bottom: 5px;color: rgb(63, 63, 63);font-size: 16px;line-height: 1.8em;letter-spacing: 0.02em;text-align: left;font-weight: normal;\"><span leaf=\"\">怎样的架构图是好的架构图</span></section></li><ul style=\"list-style-type: disc;margin-top: 8px;margin-bottom: 8px;margin-left: 0px;margin-right: 0px;padding-top: 0px;padding-bottom: 0px;padding-left: 25px;padding-right: 0px;color: rgb(0, 0, 0);\" class=\"list-paddingleft-1\"><li><section style=\"margin-top: 5px;margin-bottom: 5px;color: rgb(63, 63, 63);font-size: 16px;line-height: 1.8em;letter-spacing: 0.02em;text-align: left;font-weight: normal;\"><span leaf=\"\">1、方框代表什么？</span></section></li><li><section style=\"margin-top: 5px;margin-bottom: 5px;color: rgb(63, 63, 63);font-size: 16px;line-height: 1.8em;letter-spacing: 0.02em;text-align: left;font-weight: normal;\"><span leaf=\"\">2、虚线、实线什么意思？箭头什么意思？颜色什么意思？</span></section></li><li><section style=\"margin-top: 5px;margin-bottom: 5px;color: rgb(63, 63, 63);font-size: 16px;line-height: 1.8em;letter-spacing: 0.02em;text-align: left;font-weight: normal;\"><span leaf=\"\">3、运行时与编译时冲突？层级冲突？</span></section></li></ul><li><section style=\"margin-top: 5px;margin-bottom: 5px;color: rgb(63, 63, 63);font-size: 16px;line-height: 1.8em;letter-spacing: 0.02em;text-align: left;font-weight: normal;\"><span leaf=\"\">本文推荐的画图方法</span></section></li><ul style=\"list-style-type: disc;margin-top: 8px;margin-bottom: 8px;margin-left: 0px;margin-right: 0px;padding-top: 0px;padding-bottom: 0px;padding-left: 25px;padding-right: 0px;color: rgb(0, 0, 0);\" class=\"list-paddingleft-1\"><li><section style=\"margin-top: 5px;margin-bottom: 5px;color: rgb(63, 63, 63);font-size: 16px;line-height: 1.8em;letter-spacing: 0.02em;text-align: left;font-weight: normal;\"><span leaf=\"\">1、语境图（System Context Diagram）</span></section></li><li><section style=\"margin-top: 5px;margin-bottom: 5px;color: rgb(63, 63, 63);font-size: 16px;line-height: 1.8em;letter-spacing: 0.02em;text-align: left;font-weight: normal;\"><span leaf=\"\">2、容器图（Container Diagram）</span></section></li><li><section style=\"margin-top: 5px;margin-bottom: 5px;color: rgb(63, 63, 63);font-size: 16px;line-height: 1.8em;letter-spacing: 0.02em;text-align: left;font-weight: normal;\"><span leaf=\"\">3、组件图（Component Diagram）</span></section></li><li><section style=\"margin-top: 5px;margin-bottom: 5px;color: rgb(63, 63, 63);font-size: 16px;line-height: 1.8em;letter-spacing: 0.02em;text-align: left;font-weight: normal;\"><span leaf=\"\">4、类图（Code/Class Diagram）</span></section></li></ul><li><section style=\"margin-top: 5px;margin-bottom: 5px;color: rgb(63, 63, 63);font-size: 16px;line-height: 1.8em;letter-spacing: 0.02em;text-align: left;font-weight: normal;\"><span leaf=\"\">案例分享</span></section></li><li><section style=\"margin-top: 5px;margin-bottom: 5px;color: rgb(63, 63, 63);font-size: 16px;line-height: 1.8em;letter-spacing: 0.02em;text-align: left;font-weight: normal;\"><span leaf=\"\">参考资料</span></section></li></ul><hr style=\"margin-top: 10px;margin-bottom: 10px;margin-left: 0px;margin-right: 0px;padding-top: 0px;padding-bottom: 0px;padding-left: 0px;padding-right: 0px;border-top-style: none;border-bottom-style: none;border-left-style: none;border-right-style: none;border-top-width: 1px;border-bottom-width: 3px;border-left-width: 3px;border-right-width: 3px;border-top-color: rgb(0, 0, 0);border-bottom-color: rgba(0, 0, 0, 0.4);border-left-color: rgba(0, 0, 0, 0.4);border-right-color: rgba(0, 0, 0, 0.4);border-top-left-radius: 0px;border-top-right-radius: 0px;border-bottom-right-radius: 0px;border-bottom-left-radius: 0px;background-attachment: scroll;background-clip: border-box;background-color: rgba(255, 255, 255, 0);background-image: linear-gradient(90deg, rgba(93, 186, 133, 0) 0%, rgba(93, 186, 133, 0.75) 50%, rgba(93, 186, 133, 0) 100%);background-origin: padding-box;background-position-x: 0%;background-position-y: 0%;background-repeat: no-repeat;background-size: auto;width: auto;height: 2px;\"><p data-tool=\"mdnice编辑器\" style=\"color: rgb(63, 63, 63);font-size: 16px;line-height: 1.8em;letter-spacing: 0.02em;text-align: left;text-indent: 0em;margin-top: 0px;margin-bottom: 0px;margin-left: 0px;margin-right: 0px;padding-top: 16px;padding-bottom: 8px;padding-left: 0px;padding-right: 0px;\"><span leaf=\"\">技术传播的价值，不仅仅体现在通过商业化产品和开源项目来缩短我们构建应用的路径，加速业务的上线速率，也体现在优秀工程师在工作效率提升、产品性能优化和用户体验改善等经验方面的分享，以提高我们的专业能力。</span></p><p data-tool=\"mdnice编辑器\" style=\"color: rgb(63, 63, 63);font-size: 16px;line-height: 1.8em;letter-spacing: 0.02em;text-align: left;text-indent: 0em;margin-top: 0px;margin-bottom: 0px;margin-left: 0px;margin-right: 0px;padding-top: 16px;padding-bottom: 8px;padding-left: 0px;padding-right: 0px;\"><span leaf=\"\">本文作者阿里巴巴技术专家三画，分享了自己和团队在画好架构图方面的理念和经验，首发于阿里内部技术分享平台，阿里巴巴中间件授权转载，梓敬、鹏升和余乐对此文亦有贡献。</span></p><p data-tool=\"mdnice编辑器\" style=\"color: rgb(63, 63, 63);font-size: 16px;line-height: 1.8em;letter-spacing: 0.02em;text-align: left;text-indent: 0em;margin-top: 0px;margin-bottom: 0px;margin-left: 0px;margin-right: 0px;padding-top: 16px;padding-bottom: 8px;padding-left: 0px;padding-right: 0px;\"><span leaf=\"\">当我们想用一张或几张图来描述我们的系统时，是不是经常遇到以下情况：</span></p><ul style=\"list-style-type: disc;margin-top: 8px;margin-bottom: 8px;margin-left: 0px;margin-right: 0px;padding-top: 0px;padding-bottom: 0px;padding-left: 25px;padding-right: 0px;color: rgb(0, 0, 0);\" class=\"list-paddingleft-1\"><li><section style=\"margin-top: 5px;margin-bottom: 5px;color: rgb(63, 63, 63);font-size: 16px;line-height: 1.8em;letter-spacing: 0.02em;text-align: left;font-weight: normal;\"><span leaf=\"\">对着画布无从下手、删了又来？</span></section></li><li><section style=\"margin-top: 5px;margin-bottom: 5px;color: rgb(63, 63, 63);font-size: 16px;line-height: 1.8em;letter-spacing: 0.02em;text-align: left;font-weight: normal;\"><span leaf=\"\">用一张图描述我的系统，并且让产品、运营、开发都能看明白？</span></section></li><li><section style=\"margin-top: 5px;margin-bottom: 5px;color: rgb(63, 63, 63);font-size: 16px;line-height: 1.8em;letter-spacing: 0.02em;text-align: left;font-weight: normal;\"><span leaf=\"\">画了一半的图还不清楚受众是谁？</span></section></li><li><section style=\"margin-top: 5px;margin-bottom: 5px;color: rgb(63, 63, 63);font-size: 16px;line-height: 1.8em;letter-spacing: 0.02em;text-align: left;font-weight: normal;\"><span leaf=\"\">画出来的图到底是产品图功能图还是技术图又或是大杂烩？</span></section></li><li><section style=\"margin-top: 5px;margin-bottom: 5px;color: rgb(63, 63, 63);font-size: 16px;line-height: 1.8em;letter-spacing: 0.02em;text-align: left;font-weight: normal;\"><span leaf=\"\">图上的框框有点少是不是要找点儿框框加进来？</span></section></li><li><section style=\"margin-top: 5px;margin-bottom: 5px;color: rgb(63, 63, 63);font-size: 16px;line-height: 1.8em;letter-spacing: 0.02em;text-align: left;font-weight: normal;\"><span leaf=\"\">布局怎么画都不满意……</span></section></li></ul><p data-tool=\"mdnice编辑器\" style=\"color: rgb(63, 63, 63);font-size: 16px;line-height: 1.8em;letter-spacing: 0.02em;text-align: left;text-indent: 0em;margin-top: 0px;margin-bottom: 0px;margin-left: 0px;margin-right: 0px;padding-top: 16px;padding-bottom: 8px;padding-left: 0px;padding-right: 0px;\"><span leaf=\"\">如果有同样的困惑，本文将介绍一种画图的方法论，来让架构图更清晰。</span></p><h2 data-cacheurl=\"\" data-remoteid=\"\" data-tool=\"mdnice编辑器\" style=\"margin-top: 30px;margin-bottom: 15px;margin-left: 0px;margin-right: 0px;padding-left: 0px;padding-right: 0px;background-attachment: scroll;background-clip: border-box;background-color: transparent;background-origin: padding-box;background-position-x: 50%;background-position-y: 50%;background-repeat: no-repeat;background-size: 63px;width: auto;height: auto;align-items: unset;border-top-style: none;border-bottom-style: none;border-left-style: none;border-right-style: none;border-top-width: 1px;border-bottom-width: 1px;border-left-width: 1px;border-right-width: 1px;border-top-color: rgb(0, 0, 0);border-bottom-color: rgb(0, 0, 0);border-left-color: rgb(0, 0, 0);border-right-color: rgb(0, 0, 0);border-top-left-radius: 0px;border-top-right-radius: 0px;border-bottom-left-radius: 0px;border-bottom-right-radius: 0px;box-shadow: none;display: flex;flex-direction: unset;float: unset;justify-content: center;line-height: 1.5em;overflow-x: unset;overflow-y: unset;padding-top: 0px;padding-bottom: 0px; text-align: left;text-shadow: none;transform: none;-webkit-box-reflect: unset;background-image: url(&quot;https://mmbiz.qpic.cn/sz_mmbiz_png/knmrNHnmCLE2wTd9jX9u0GWe2XtE2gG0Bs9yY5iagVWlfZlVX6Pj0NCx7WsN5oabG7Tc4J2CibjIWuTgUfeVcVVQ/640?wx_fmt=png&amp;from=appmsg&quot;);\"><span style=\"display: none;\"></span><span style=\" font-size: 18px;color: rgb(72, 179, 120);line-height: 2.4em;letter-spacing: 0em;margin-top: 38px;margin-bottom: 10px;border-top-style: none;border-bottom-style: none;border-left-style: none;border-right-style: none;border-top-width: 1px;border-bottom-width: 1px;border-left-width: 1px;border-right-width: 1px;border-top-color: rgb(0, 0, 0);border-bottom-color: rgb(0, 0, 0);border-left-color: rgb(0, 0, 0);border-right-color: rgb(0, 0, 0);border-top-left-radius: 0px;border-top-right-radius: 0px;border-bottom-left-radius: 0px;border-bottom-right-radius: 0px;align-items: unset;background-attachment: scroll;background-clip: border-box;background-color: transparent;background-image: none;background-origin: padding-box;background-position-x: 0%;background-position-y: 0%;background-repeat: no-repeat;background-size: auto;box-shadow: none;display: inline-block;font-weight: bold;flex-direction: unset;float: unset;height: 38px;justify-content: unset;margin-left: 0px;margin-right: 0px;overflow-x: unset;overflow-y: unset;padding-top: 0px;padding-bottom: 0px;padding-left: 0px;padding-right: 0px; text-align: center;text-indent: 0em;text-shadow: none;transform: none;width: auto;-webkit-box-reflect: unset; \"><strong style=\" align-items: unset;background-attachment: scroll;background-clip: border-box;background-color: transparent;background-image: none;background-origin: padding-box;background-position-x: 0%;background-position-y: 0%;background-repeat: no-repeat;background-size: auto;border-top-style: none;border-bottom-style: none;border-left-style: none;border-right-style: none;border-top-width: 1px;border-bottom-width: 1px;border-left-width: 1px;border-right-width: 1px;border-top-color: rgb(0, 0, 0);border-bottom-color: rgb(0, 0, 0);border-left-color: rgb(0, 0, 0);border-right-color: rgb(0, 0, 0);border-top-left-radius: 0px;border-top-right-radius: 0px;border-bottom-left-radius: 0px;border-bottom-right-radius: 0px;box-shadow: none;color: rgb(0, 0, 0);display: inline-block;font-size: 22px;font-weight: bold;flex-direction: unset;float: unset;height: auto;justify-content: unset;letter-spacing: 0px;line-height: 1.5em;margin-top: 0px;margin-bottom: 0px;margin-left: 0px;margin-right: 0px;overflow-x: unset;overflow-y: unset;padding-top: 0px;padding-bottom: 0px;padding-left: 0px;padding-right: 0px; text-align: left;text-indent: 0em;text-shadow: none;transform: none;width: auto;-webkit-box-reflect: unset; \"><span leaf=\"\">先厘清一些基础概念</span></strong></span><span style=\"display: none;\"></span></h2><h3 data-tool=\"mdnice编辑器\" style=\" margin-top: 30px;margin-bottom: 15px;margin-left: 0px;margin-right: 0px;padding-top: 0px;padding-bottom: 0px;padding-left: 0px;padding-right: 0px;align-items: unset;background-attachment: scroll;background-clip: border-box;background-color: transparent;background-image: none;background-origin: padding-box;background-position-x: 0%;background-position-y: 0%;background-repeat: no-repeat;background-size: auto;border-top-style: none;border-bottom-style: none;border-left-style: none;border-right-style: none;border-top-width: 1px;border-bottom-width: 1px;border-left-width: 1px;border-right-width: 1px;border-top-color: rgb(0, 0, 0);border-bottom-color: rgb(0, 0, 0);border-left-color: rgb(0, 0, 0);border-right-color: rgb(0, 0, 0);border-top-left-radius: 0px;border-top-right-radius: 0px;border-bottom-left-radius: 0px;border-bottom-right-radius: 0px;box-shadow: none;display: block;flex-direction: unset;float: unset;height: auto;justify-content: unset;line-height: 1.5em;overflow-x: unset;overflow-y: unset; text-align: left;text-shadow: none;transform: none;width: auto;-webkit-box-reflect: unset; \"><span data-cacheurl=\"\" data-remoteid=\"\" style=\"background-attachment: scroll;background-clip: border-box;background-color: transparent;background-origin: padding-box;background-position-x: 0%;background-position-y: 0%;background-repeat: no-repeat;background-size: 15px 15px;width: 15px;height: 15px;align-items: unset;border-top-style: none;border-bottom-style: none;border-left-style: none;border-right-style: none;border-top-width: 1px;border-bottom-width: 1px;border-left-width: 1px;border-right-width: 1px;border-top-color: rgb(0, 0, 0);border-bottom-color: rgb(0, 0, 0);border-left-color: rgb(0, 0, 0);border-right-color: rgb(0, 0, 0);border-top-left-radius: 0px;border-top-right-radius: 0px;border-bottom-left-radius: 0px;border-bottom-right-radius: 0px;box-shadow: none;color: rgb(0, 0, 0);display: inline-block;font-size: 22px;font-weight: bold;flex-direction: unset;float: unset;justify-content: unset;letter-spacing: 0px;line-height: 1.5em;margin-top: 0px;margin-bottom: -2px;margin-left: 0px;margin-right: 0px;overflow-x: unset;overflow-y: unset;padding-top: 0px;padding-bottom: 0px;padding-left: 0px;padding-right: 0px; text-align: left;text-indent: 0em;text-shadow: none;transform: none;-webkit-box-reflect: unset;background-image: url(&quot;https://mmbiz.qpic.cn/sz_mmbiz_png/knmrNHnmCLE2wTd9jX9u0GWe2XtE2gG0T83kl6QcwWG6uyzeibHmM8L1OBErtgTKOKFub6CcZrDE4RZCITBZTcQ/640?wx_fmt=png&amp;from=appmsg&quot;);\"></span><span style=\"display: none;\"></span><span style=\" font-size: 16px;color: rgb(72, 179, 120);line-height: 1.5em;letter-spacing: 0em;align-items: unset;background-attachment: scroll;background-clip: border-box;background-color: transparent;background-image: none;background-origin: padding-box;background-position-x: 0%;background-position-y: 0%;background-repeat: no-repeat;background-size: auto;border-top-style: none;border-bottom-style: none;border-left-style: none;border-right-style: none;border-top-width: 1px;border-bottom-width: 1px;border-left-width: 1px;border-right-width: 1px;border-top-color: rgb(0, 0, 0);border-bottom-color: rgb(0, 0, 0);border-left-color: rgb(0, 0, 0);border-right-color: rgb(0, 0, 0);border-top-left-radius: 0px;border-top-right-radius: 0px;border-bottom-left-radius: 0px;border-bottom-right-radius: 0px;box-shadow: none;display: inline-block;font-weight: bold;flex-direction: unset;float: unset;height: auto;justify-content: unset;margin-top: 0px;margin-bottom: 0px;margin-left: 8px;margin-right: 0px;overflow-x: unset;overflow-y: unset;padding-top: 0px;padding-bottom: 0px;padding-left: 0px;padding-right: 0px; text-align: left;text-indent: 0em;text-shadow: none;transform: none;width: auto;-webkit-box-reflect: unset; \"><span leaf=\"\">1、什么是架构</span></span><span style=\"display: none;\"></span></h3><p data-tool=\"mdnice编辑器\" style=\"color: rgb(63, 63, 63);font-size: 16px;line-height: 1.8em;letter-spacing: 0.02em;text-align: left;text-indent: 0em;margin-top: 0px;margin-bottom: 0px;margin-left: 0px;margin-right: 0px;padding-top: 16px;padding-bottom: 8px;padding-left: 0px;padding-right: 0px;\"><span leaf=\"\">架构就是对系统中的实体以及实体之间的关系所进行的抽象描述，是一系列的决策。</span></p><p data-tool=\"mdnice编辑器\" style=\"color: rgb(63, 63, 63);font-size: 16px;line-height: 1.8em;letter-spacing: 0.02em;text-align: left;text-indent: 0em;margin-top: 0px;margin-bottom: 0px;margin-left: 0px;margin-right: 0px;padding-top: 16px;padding-bottom: 8px;padding-left: 0px;padding-right: 0px;\"><span leaf=\"\">架构是结构和愿景。</span></p><p data-tool=\"mdnice编辑器\" style=\"color: rgb(63, 63, 63);font-size: 16px;line-height: 1.8em;letter-spacing: 0.02em;text-align: left;text-indent: 0em;margin-top: 0px;margin-bottom: 0px;margin-left: 0px;margin-right: 0px;padding-top: 16px;padding-bottom: 8px;padding-left: 0px;padding-right: 0px;\"><span leaf=\"\">系统架构是概念的体现，是对物/信息的功能与形式元素之间的对应情况所做的分配，是对元素之间的关系以及元素同周边环境之间的关系所做的定义。</span></p><p data-tool=\"mdnice编辑器\" style=\"color: rgb(63, 63, 63);font-size: 16px;line-height: 1.8em;letter-spacing: 0.02em;text-align: left;text-indent: 0em;margin-top: 0px;margin-bottom: 0px;margin-left: 0px;margin-right: 0px;padding-top: 16px;padding-bottom: 8px;padding-left: 0px;padding-right: 0px;\"><span leaf=\"\">做好架构是个复杂的任务，也是个很大的话题，本篇就不做深入了。有了架构之后，就需要让干系人理解、遵循相关决策。</span></p><h3 data-tool=\"mdnice编辑器\" style=\" margin-top: 30px;margin-bottom: 15px;margin-left: 0px;margin-right: 0px;padding-top: 0px;padding-bottom: 0px;padding-left: 0px;padding-right: 0px;align-items: unset;background-attachment: scroll;background-clip: border-box;background-color: transparent;background-image: none;background-origin: padding-box;background-position-x: 0%;background-position-y: 0%;background-repeat: no-repeat;background-size: auto;border-top-style: none;border-bottom-style: none;border-left-style: none;border-right-style: none;border-top-width: 1px;border-bottom-width: 1px;border-left-width: 1px;border-right-width: 1px;border-top-color: rgb(0, 0, 0);border-bottom-color: rgb(0, 0, 0);border-left-color: rgb(0, 0, 0);border-right-color: rgb(0, 0, 0);border-top-left-radius: 0px;border-top-right-radius: 0px;border-bottom-left-radius: 0px;border-bottom-right-radius: 0px;box-shadow: none;display: block;flex-direction: unset;float: unset;height: auto;justify-content: unset;line-height: 1.5em;overflow-x: unset;overflow-y: unset; text-align: left;text-shadow: none;transform: none;width: auto;-webkit-box-reflect: unset; \"><span data-cacheurl=\"\" data-remoteid=\"\" style=\"background-attachment: scroll;background-clip: border-box;background-color: transparent;background-origin: padding-box;background-position-x: 0%;background-position-y: 0%;background-repeat: no-repeat;background-size: 15px 15px;width: 15px;height: 15px;align-items: unset;border-top-style: none;border-bottom-style: none;border-left-style: none;border-right-style: none;border-top-width: 1px;border-bottom-width: 1px;border-left-width: 1px;border-right-width: 1px;border-top-color: rgb(0, 0, 0);border-bottom-color: rgb(0, 0, 0);border-left-color: rgb(0, 0, 0);border-right-color: rgb(0, 0, 0);border-top-left-radius: 0px;border-top-right-radius: 0px;border-bottom-left-radius: 0px;border-bottom-right-radius: 0px;box-shadow: none;color: rgb(0, 0, 0);display: inline-block;font-size: 22px;font-weight: bold;flex-direction: unset;float: unset;justify-content: unset;letter-spacing: 0px;line-height: 1.5em;margin-top: 0px;margin-bottom: -2px;margin-left: 0px;margin-right: 0px;overflow-x: unset;overflow-y: unset;padding-top: 0px;padding-bottom: 0px;padding-left: 0px;padding-right: 0px; text-align: left;text-indent: 0em;text-shadow: none;transform: none;-webkit-box-reflect: unset;background-image: url(&quot;https://mmbiz.qpic.cn/sz_mmbiz_png/knmrNHnmCLE2wTd9jX9u0GWe2XtE2gG0T83kl6QcwWG6uyzeibHmM8L1OBErtgTKOKFub6CcZrDE4RZCITBZTcQ/640?wx_fmt=png&amp;from=appmsg&quot;);\"></span><span style=\"display: none;\"></span><span style=\" font-size: 16px;color: rgb(72, 179, 120);line-height: 1.5em;letter-spacing: 0em;align-items: unset;background-attachment: scroll;background-clip: border-box;background-color: transparent;background-image: none;background-origin: padding-box;background-position-x: 0%;background-position-y: 0%;background-repeat: no-repeat;background-size: auto;border-top-style: none;border-bottom-style: none;border-left-style: none;border-right-style: none;border-top-width: 1px;border-bottom-width: 1px;border-left-width: 1px;border-right-width: 1px;border-top-color: rgb(0, 0, 0);border-bottom-color: rgb(0, 0, 0);border-left-color: rgb(0, 0, 0);border-right-color: rgb(0, 0, 0);border-top-left-radius: 0px;border-top-right-radius: 0px;border-bottom-left-radius: 0px;border-bottom-right-radius: 0px;box-shadow: none;display: inline-block;font-weight: bold;flex-direction: unset;float: unset;height: auto;justify-content: unset;margin-top: 0px;margin-bottom: 0px;margin-left: 8px;margin-right: 0px;overflow-x: unset;overflow-y: unset;padding-top: 0px;padding-bottom: 0px;padding-left: 0px;padding-right: 0px; text-align: left;text-indent: 0em;text-shadow: none;transform: none;width: auto;-webkit-box-reflect: unset; \"><span leaf=\"\">2、什么是架构图</span></span><span style=\"display: none;\"></span></h3><p data-tool=\"mdnice编辑器\" style=\"color: rgb(63, 63, 63);font-size: 16px;line-height: 1.8em;letter-spacing: 0.02em;text-align: left;text-indent: 0em;margin-top: 0px;margin-bottom: 0px;margin-left: 0px;margin-right: 0px;padding-top: 16px;padding-bottom: 8px;padding-left: 0px;padding-right: 0px;\"><span leaf=\"\">系统架构图是为了抽象的表示软件系统的整体轮廓和各个组件之间的相互关系和约束边界，以及软件系统的物理部署和软件系统的演进方向的整体视图。</span></p><h3 data-tool=\"mdnice编辑器\" style=\" margin-top: 30px;margin-bottom: 15px;margin-left: 0px;margin-right: 0px;padding-top: 0px;padding-bottom: 0px;padding-left: 0px;padding-right: 0px;align-items: unset;background-attachment: scroll;background-clip: border-box;background-color: transparent;background-image: none;background-origin: padding-box;background-position-x: 0%;background-position-y: 0%;background-repeat: no-repeat;background-size: auto;border-top-style: none;border-bottom-style: none;border-left-style: none;border-right-style: none;border-top-width: 1px;border-bottom-width: 1px;border-left-width: 1px;border-right-width: 1px;border-top-color: rgb(0, 0, 0);border-bottom-color: rgb(0, 0, 0);border-left-color: rgb(0, 0, 0);border-right-color: rgb(0, 0, 0);border-top-left-radius: 0px;border-top-right-radius: 0px;border-bottom-left-radius: 0px;border-bottom-right-radius: 0px;box-shadow: none;display: block;flex-direction: unset;float: unset;height: auto;justify-content: unset;line-height: 1.5em;overflow-x: unset;overflow-y: unset; text-align: left;text-shadow: none;transform: none;width: auto;-webkit-box-reflect: unset; \"><span data-cacheurl=\"\" data-remoteid=\"\" style=\"background-attachment: scroll;background-clip: border-box;background-color: transparent;background-origin: padding-box;background-position-x: 0%;background-position-y: 0%;background-repeat: no-repeat;background-size: 15px 15px;width: 15px;height: 15px;align-items: unset;border-top-style: none;border-bottom-style: none;border-left-style: none;border-right-style: none;border-top-width: 1px;border-bottom-width: 1px;border-left-width: 1px;border-right-width: 1px;border-top-color: rgb(0, 0, 0);border-bottom-color: rgb(0, 0, 0);border-left-color: rgb(0, 0, 0);border-right-color: rgb(0, 0, 0);border-top-left-radius: 0px;border-top-right-radius: 0px;border-bottom-left-radius: 0px;border-bottom-right-radius: 0px;box-shadow: none;color: rgb(0, 0, 0);display: inline-block;font-size: 22px;font-weight: bold;flex-direction: unset;float: unset;justify-content: unset;letter-spacing: 0px;line-height: 1.5em;margin-top: 0px;margin-bottom: -2px;margin-left: 0px;margin-right: 0px;overflow-x: unset;overflow-y: unset;padding-top: 0px;padding-bottom: 0px;padding-left: 0px;padding-right: 0px; text-align: left;text-indent: 0em;text-shadow: none;transform: none;-webkit-box-reflect: unset;background-image: url(&quot;https://mmbiz.qpic.cn/sz_mmbiz_png/knmrNHnmCLE2wTd9jX9u0GWe2XtE2gG0T83kl6QcwWG6uyzeibHmM8L1OBErtgTKOKFub6CcZrDE4RZCITBZTcQ/640?wx_fmt=png&amp;from=appmsg&quot;);\"></span><span style=\"display: none;\"></span><span style=\" font-size: 16px;color: rgb(72, 179, 120);line-height: 1.5em;letter-spacing: 0em;align-items: unset;background-attachment: scroll;background-clip: border-box;background-color: transparent;background-image: none;background-origin: padding-box;background-position-x: 0%;background-position-y: 0%;background-repeat: no-repeat;background-size: auto;border-top-style: none;border-bottom-style: none;border-left-style: none;border-right-style: none;border-top-width: 1px;border-bottom-width: 1px;border-left-width: 1px;border-right-width: 1px;border-top-color: rgb(0, 0, 0);border-bottom-color: rgb(0, 0, 0);border-left-color: rgb(0, 0, 0);border-right-color: rgb(0, 0, 0);border-top-left-radius: 0px;border-top-right-radius: 0px;border-bottom-left-radius: 0px;border-bottom-right-radius: 0px;box-shadow: none;display: inline-block;font-weight: bold;flex-direction: unset;float: unset;height: auto;justify-content: unset;margin-top: 0px;margin-bottom: 0px;margin-left: 8px;margin-right: 0px;overflow-x: unset;overflow-y: unset;padding-top: 0px;padding-bottom: 0px;padding-left: 0px;padding-right: 0px; text-align: left;text-indent: 0em;text-shadow: none;transform: none;width: auto;-webkit-box-reflect: unset; \"><span leaf=\"\">3、架构图的作用</span></span><span style=\"display: none;\"></span></h3><p data-tool=\"mdnice编辑器\" style=\"color: rgb(63, 63, 63);font-size: 16px;line-height: 1.8em;letter-spacing: 0.02em;text-align: left;text-indent: 0em;margin-top: 0px;margin-bottom: 0px;margin-left: 0px;margin-right: 0px;padding-top: 16px;padding-bottom: 8px;padding-left: 0px;padding-right: 0px;\"><span leaf=\"\">一图胜千言。要让干系人理解、遵循架构决策，就需要把架构信息传递出去。架构图就是一个很好的载体。那么，画架构图是为了：</span></p><ul style=\"list-style-type: disc;margin-top: 8px;margin-bottom: 8px;margin-left: 0px;margin-right: 0px;padding-top: 0px;padding-bottom: 0px;padding-left: 25px;padding-right: 0px;color: rgb(0, 0, 0);\" class=\"list-paddingleft-1\"><li><section style=\"margin-top: 5px;margin-bottom: 5px;color: rgb(63, 63, 63);font-size: 16px;line-height: 1.8em;letter-spacing: 0.02em;text-align: left;font-weight: normal;\"><span leaf=\"\">解决沟通障碍</span></section></li><li><section style=\"margin-top: 5px;margin-bottom: 5px;color: rgb(63, 63, 63);font-size: 16px;line-height: 1.8em;letter-spacing: 0.02em;text-align: left;font-weight: normal;\"><span leaf=\"\">达成共识</span></section></li><li><section style=\"margin-top: 5px;margin-bottom: 5px;color: rgb(63, 63, 63);font-size: 16px;line-height: 1.8em;letter-spacing: 0.02em;text-align: left;font-weight: normal;\"><span leaf=\"\">减少歧义</span></section></li></ul><figure data-tool=\"mdnice编辑器\" style=\"margin-top: 10px;margin-bottom: 10px;margin-left: 0px;margin-right: 0px;padding-top: 0px;padding-bottom: 0px;padding-left: 0px;padding-right: 0px;display: flex;flex-direction: column;justify-content: center;align-items: center;\"><span leaf=\"\"><img data-src=\"https://mmbiz.qpic.cn/mmbiz_jpg/6mychickmupWDcvUWtYjTibVbFbG6N0LPJyythz5iateukvGeJ4nyO6nfAqbf2y5tkMMH8BibXGSh6T14w1rBq7SVQ/640?wx_fmt=other&amp;wxfrom=5&amp;wx_lazy=1&amp;wx_co=1&amp;tp=wxpic\" alt=\"图片\" class=\"rich_pages wxw-img\" data-ratio=\"1.4981481481481482\" data-w=\"1080\" style=\"display: block;margin-top: 0px;margin-right: auto;margin-bottom: 0px;margin-left: auto;max-width: 100%;border-top-style: none;border-bottom-style: none;border-left-style: none;border-right-style: none;border-top-width: 3px;border-bottom-width: 3px;border-left-width: 3px;border-right-width: 3px;border-top-color: rgba(0, 0, 0, 0.4);border-bottom-color: rgba(0, 0, 0, 0.4);border-left-color: rgba(0, 0, 0, 0.4);border-right-color: rgba(0, 0, 0, 0.4);border-top-left-radius: 4px;border-top-right-radius: 4px;border-bottom-right-radius: 4px;border-bottom-left-radius: 4px;object-fit: fill;box-shadow: rgba(0, 0, 0, 0) 0px 0px 0px 0px;\"></span><figcaption style=\"color: rgb(136, 136, 136);font-size: 12px;line-height: 1.5em;letter-spacing: 0em;text-align: center;font-weight: normal;margin-top: 5px;margin-bottom: 0px;margin-left: 0px;margin-right: 0px;padding-top: 0px;padding-bottom: 0px;padding-left: 0px;padding-right: 0px;\"><span leaf=\"\">图片</span></figcaption></figure><h3 data-tool=\"mdnice编辑器\" style=\" margin-top: 30px;margin-bottom: 15px;margin-left: 0px;margin-right: 0px;padding-top: 0px;padding-bottom: 0px;padding-left: 0px;padding-right: 0px;align-items: unset;background-attachment: scroll;background-clip: border-box;background-color: transparent;background-image: none;background-origin: padding-box;background-position-x: 0%;background-position-y: 0%;background-repeat: no-repeat;background-size: auto;border-top-style: none;border-bottom-style: none;border-left-style: none;border-right-style: none;border-top-width: 1px;border-bottom-width: 1px;border-left-width: 1px;border-right-width: 1px;border-top-color: rgb(0, 0, 0);border-bottom-color: rgb(0, 0, 0);border-left-color: rgb(0, 0, 0);border-right-color: rgb(0, 0, 0);border-top-left-radius: 0px;border-top-right-radius: 0px;border-bottom-left-radius: 0px;border-bottom-right-radius: 0px;box-shadow: none;display: block;flex-direction: unset;float: unset;height: auto;justify-content: unset;line-height: 1.5em;overflow-x: unset;overflow-y: unset; text-align: left;text-shadow: none;transform: none;width: auto;-webkit-box-reflect: unset; \"><span data-cacheurl=\"\" data-remoteid=\"\" style=\"background-attachment: scroll;background-clip: border-box;background-color: transparent;background-origin: padding-box;background-position-x: 0%;background-position-y: 0%;background-repeat: no-repeat;background-size: 15px 15px;width: 15px;height: 15px;align-items: unset;border-top-style: none;border-bottom-style: none;border-left-style: none;border-right-style: none;border-top-width: 1px;border-bottom-width: 1px;border-left-width: 1px;border-right-width: 1px;border-top-color: rgb(0, 0, 0);border-bottom-color: rgb(0, 0, 0);border-left-color: rgb(0, 0, 0);border-right-color: rgb(0, 0, 0);border-top-left-radius: 0px;border-top-right-radius: 0px;border-bottom-left-radius: 0px;border-bottom-right-radius: 0px;box-shadow: none;color: rgb(0, 0, 0);display: inline-block;font-size: 22px;font-weight: bold;flex-direction: unset;float: unset;justify-content: unset;letter-spacing: 0px;line-height: 1.5em;margin-top: 0px;margin-bottom: -2px;margin-left: 0px;margin-right: 0px;overflow-x: unset;overflow-y: unset;padding-top: 0px;padding-bottom: 0px;padding-left: 0px;padding-right: 0px; text-align: left;text-indent: 0em;text-shadow: none;transform: none;-webkit-box-reflect: unset;background-image: url(&quot;https://mmbiz.qpic.cn/sz_mmbiz_png/knmrNHnmCLE2wTd9jX9u0GWe2XtE2gG0T83kl6QcwWG6uyzeibHmM8L1OBErtgTKOKFub6CcZrDE4RZCITBZTcQ/640?wx_fmt=png&amp;from=appmsg&quot;);\"></span><span style=\"display: none;\"></span><span style=\" font-size: 16px;color: rgb(72, 179, 120);line-height: 1.5em;letter-spacing: 0em;align-items: unset;background-attachment: scroll;background-clip: border-box;background-color: transparent;background-image: none;background-origin: padding-box;background-position-x: 0%;background-position-y: 0%;background-repeat: no-repeat;background-size: auto;border-top-style: none;border-bottom-style: none;border-left-style: none;border-right-style: none;border-top-width: 1px;border-bottom-width: 1px;border-left-width: 1px;border-right-width: 1px;border-top-color: rgb(0, 0, 0);border-bottom-color: rgb(0, 0, 0);border-left-color: rgb(0, 0, 0);border-right-color: rgb(0, 0, 0);border-top-left-radius: 0px;border-top-right-radius: 0px;border-bottom-left-radius: 0px;border-bottom-right-radius: 0px;box-shadow: none;display: inline-block;font-weight: bold;flex-direction: unset;float: unset;height: auto;justify-content: unset;margin-top: 0px;margin-bottom: 0px;margin-left: 8px;margin-right: 0px;overflow-x: unset;overflow-y: unset;padding-top: 0px;padding-bottom: 0px;padding-left: 0px;padding-right: 0px; text-align: left;text-indent: 0em;text-shadow: none;transform: none;width: auto;-webkit-box-reflect: unset; \"><span leaf=\"\">4、架构图分类</span></span><span style=\"display: none;\"></span></h3><p data-tool=\"mdnice编辑器\" style=\"color: rgb(63, 63, 63);font-size: 16px;line-height: 1.8em;letter-spacing: 0.02em;text-align: left;text-indent: 0em;margin-top: 0px;margin-bottom: 0px;margin-left: 0px;margin-right: 0px;padding-top: 16px;padding-bottom: 8px;padding-left: 0px;padding-right: 0px;\"><span leaf=\"\">搜集了很多资料，分类有很多，有一种比较流行的是4+1视图，分别为场景视图、逻辑视图、物理视图、处理流程视图和开发视图。</span></p><ul style=\"list-style-type: disc;margin-top: 8px;margin-bottom: 8px;margin-left: 0px;margin-right: 0px;padding-top: 0px;padding-bottom: 0px;padding-left: 25px;padding-right: 0px;color: rgb(0, 0, 0);\" class=\"list-paddingleft-1\"><li><section style=\"margin-top: 5px;margin-bottom: 5px;color: rgb(63, 63, 63);font-size: 16px;line-height: 1.8em;letter-spacing: 0.02em;text-align: left;font-weight: normal;\"><span leaf=\"\">场景视图</span></section></li></ul><p data-tool=\"mdnice编辑器\" style=\"color: rgb(63, 63, 63);font-size: 16px;line-height: 1.8em;letter-spacing: 0.02em;text-align: left;text-indent: 0em;margin-top: 0px;margin-bottom: 0px;margin-left: 0px;margin-right: 0px;padding-top: 16px;padding-bottom: 8px;padding-left: 0px;padding-right: 0px;\"><span leaf=\"\">场景视图用于描述系统的参与者与功能用例间的关系，反映系统的最终需求和交互设计，通常由用例图表示。</span></p><figure data-tool=\"mdnice编辑器\" style=\"margin-top: 10px;margin-bottom: 10px;margin-left: 0px;margin-right: 0px;padding-top: 0px;padding-bottom: 0px;padding-left: 0px;padding-right: 0px;display: flex;flex-direction: column;justify-content: center;align-items: center;\"><span leaf=\"\"><img data-src=\"https://mmbiz.qpic.cn/mmbiz_jpg/6mychickmupWDcvUWtYjTibVbFbG6N0LPJIU65g44E4IfvHULKjcLGzDsLkqLSTubRiaR9ialQT7yehtvEUl8a1y2w/640?wx_fmt=other&amp;wxfrom=5&amp;wx_lazy=1&amp;wx_co=1&amp;tp=wxpic\" alt=\"图片\" class=\"rich_pages wxw-img\" data-ratio=\"0.6509259259259259\" data-w=\"1080\" style=\"display: block;margin-top: 0px;margin-right: auto;margin-bottom: 0px;margin-left: auto;max-width: 100%;border-top-style: none;border-bottom-style: none;border-left-style: none;border-right-style: none;border-top-width: 3px;border-bottom-width: 3px;border-left-width: 3px;border-right-width: 3px;border-top-color: rgba(0, 0, 0, 0.4);border-bottom-color: rgba(0, 0, 0, 0.4);border-left-color: rgba(0, 0, 0, 0.4);border-right-color: rgba(0, 0, 0, 0.4);border-top-left-radius: 4px;border-top-right-radius: 4px;border-bottom-right-radius: 4px;border-bottom-left-radius: 4px;object-fit: fill;box-shadow: rgba(0, 0, 0, 0) 0px 0px 0px 0px;\"></span><figcaption style=\"color: rgb(136, 136, 136);font-size: 12px;line-height: 1.5em;letter-spacing: 0em;text-align: center;font-weight: normal;margin-top: 5px;margin-bottom: 0px;margin-left: 0px;margin-right: 0px;padding-top: 0px;padding-bottom: 0px;padding-left: 0px;padding-right: 0px;\"><span leaf=\"\">图片</span></figcaption></figure><ul style=\"list-style-type: disc;margin-top: 8px;margin-bottom: 8px;margin-left: 0px;margin-right: 0px;padding-top: 0px;padding-bottom: 0px;padding-left: 25px;padding-right: 0px;color: rgb(0, 0, 0);\" class=\"list-paddingleft-1\"><li><section style=\"margin-top: 5px;margin-bottom: 5px;color: rgb(63, 63, 63);font-size: 16px;line-height: 1.8em;letter-spacing: 0.02em;text-align: left;font-weight: normal;\"><span leaf=\"\">逻辑视图</span></section></li></ul><p data-tool=\"mdnice编辑器\" style=\"color: rgb(63, 63, 63);font-size: 16px;line-height: 1.8em;letter-spacing: 0.02em;text-align: left;text-indent: 0em;margin-top: 0px;margin-bottom: 0px;margin-left: 0px;margin-right: 0px;padding-top: 16px;padding-bottom: 8px;padding-left: 0px;padding-right: 0px;\"><span leaf=\"\">逻辑视图用于描述系统软件功能拆解后的组件关系，组件约束和边界，反映系统整体组成与系 统如何构建的过程,通常由UML的组件图和类图来表示。</span></p><figure data-tool=\"mdnice编辑器\" style=\"margin-top: 10px;margin-bottom: 10px;margin-left: 0px;margin-right: 0px;padding-top: 0px;padding-bottom: 0px;padding-left: 0px;padding-right: 0px;display: flex;flex-direction: column;justify-content: center;align-items: center;\"><span leaf=\"\"><img data-src=\"https://mmbiz.qpic.cn/mmbiz_jpg/6mychickmupWDcvUWtYjTibVbFbG6N0LPJ3xSefic2DXTNYpOaCsvdBOGe2LhsmFafESlqJqZD7ktL4bImks4TaKw/640?wx_fmt=other&amp;wxfrom=5&amp;wx_lazy=1&amp;wx_co=1&amp;tp=wxpic\" alt=\"图片\" class=\"rich_pages wxw-img\" data-ratio=\"0.7351851851851852\" data-w=\"1080\" style=\"display: block;margin-top: 0px;margin-right: auto;margin-bottom: 0px;margin-left: auto;max-width: 100%;border-top-style: none;border-bottom-style: none;border-left-style: none;border-right-style: none;border-top-width: 3px;border-bottom-width: 3px;border-left-width: 3px;border-right-width: 3px;border-top-color: rgba(0, 0, 0, 0.4);border-bottom-color: rgba(0, 0, 0, 0.4);border-left-color: rgba(0, 0, 0, 0.4);border-right-color: rgba(0, 0, 0, 0.4);border-top-left-radius: 4px;border-top-right-radius: 4px;border-bottom-right-radius: 4px;border-bottom-left-radius: 4px;object-fit: fill;box-shadow: rgba(0, 0, 0, 0) 0px 0px 0px 0px;\"></span><figcaption style=\"color: rgb(136, 136, 136);font-size: 12px;line-height: 1.5em;letter-spacing: 0em;text-align: center;font-weight: normal;margin-top: 5px;margin-bottom: 0px;margin-left: 0px;margin-right: 0px;padding-top: 0px;padding-bottom: 0px;padding-left: 0px;padding-right: 0px;\"><span leaf=\"\">图片</span></figcaption></figure><ul style=\"list-style-type: disc;margin-top: 8px;margin-bottom: 8px;margin-left: 0px;margin-right: 0px;padding-top: 0px;padding-bottom: 0px;padding-left: 25px;padding-right: 0px;color: rgb(0, 0, 0);\" class=\"list-paddingleft-1\"><li><section style=\"margin-top: 5px;margin-bottom: 5px;color: rgb(63, 63, 63);font-size: 16px;line-height: 1.8em;letter-spacing: 0.02em;text-align: left;font-weight: normal;\"><span leaf=\"\">物理视图</span></section></li></ul><p data-tool=\"mdnice编辑器\" style=\"color: rgb(63, 63, 63);font-size: 16px;line-height: 1.8em;letter-spacing: 0.02em;text-align: left;text-indent: 0em;margin-top: 0px;margin-bottom: 0px;margin-left: 0px;margin-right: 0px;padding-top: 16px;padding-bottom: 8px;padding-left: 0px;padding-right: 0px;\"><span leaf=\"\">物理视图用于描述系统软件到物理硬件的映射关系，反映出系统的组件是如何部署到一组可 &nbsp; 计算机器节点上，用于指导软件系统的部署实施过程。</span></p><figure data-tool=\"mdnice编辑器\" style=\"margin-top: 10px;margin-bottom: 10px;margin-left: 0px;margin-right: 0px;padding-top: 0px;padding-bottom: 0px;padding-left: 0px;padding-right: 0px;display: flex;flex-direction: column;justify-content: center;align-items: center;\"><span leaf=\"\"><img data-src=\"https://mmbiz.qpic.cn/mmbiz_jpg/6mychickmupWDcvUWtYjTibVbFbG6N0LPJSkFmzMTWHQfYlCbt1l7lhGZaJ9KOURLfiagiaueicWYQgWTwy4tTfpOPA/640?wx_fmt=other&amp;wxfrom=5&amp;wx_lazy=1&amp;wx_co=1&amp;tp=wxpic\" alt=\"图片\" class=\"rich_pages wxw-img\" data-ratio=\"0.9648148148148148\" data-w=\"1080\" style=\"display: block;margin-top: 0px;margin-right: auto;margin-bottom: 0px;margin-left: auto;max-width: 100%;border-top-style: none;border-bottom-style: none;border-left-style: none;border-right-style: none;border-top-width: 3px;border-bottom-width: 3px;border-left-width: 3px;border-right-width: 3px;border-top-color: rgba(0, 0, 0, 0.4);border-bottom-color: rgba(0, 0, 0, 0.4);border-left-color: rgba(0, 0, 0, 0.4);border-right-color: rgba(0, 0, 0, 0.4);border-top-left-radius: 4px;border-top-right-radius: 4px;border-bottom-right-radius: 4px;border-bottom-left-radius: 4px;object-fit: fill;box-shadow: rgba(0, 0, 0, 0) 0px 0px 0px 0px;\"></span><figcaption style=\"color: rgb(136, 136, 136);font-size: 12px;line-height: 1.5em;letter-spacing: 0em;text-align: center;font-weight: normal;margin-top: 5px;margin-bottom: 0px;margin-left: 0px;margin-right: 0px;padding-top: 0px;padding-bottom: 0px;padding-left: 0px;padding-right: 0px;\"><span leaf=\"\">图片</span></figcaption></figure><ul style=\"list-style-type: disc;margin-top: 8px;margin-bottom: 8px;margin-left: 0px;margin-right: 0px;padding-top: 0px;padding-bottom: 0px;padding-left: 25px;padding-right: 0px;color: rgb(0, 0, 0);\" class=\"list-paddingleft-1\"><li><section style=\"margin-top: 5px;margin-bottom: 5px;color: rgb(63, 63, 63);font-size: 16px;line-height: 1.8em;letter-spacing: 0.02em;text-align: left;font-weight: normal;\"><span leaf=\"\">处理流程视图</span></section></li></ul><p data-tool=\"mdnice编辑器\" style=\"color: rgb(63, 63, 63);font-size: 16px;line-height: 1.8em;letter-spacing: 0.02em;text-align: left;text-indent: 0em;margin-top: 0px;margin-bottom: 0px;margin-left: 0px;margin-right: 0px;padding-top: 16px;padding-bottom: 8px;padding-left: 0px;padding-right: 0px;\"><span leaf=\"\">处理流程视图用于描述系统软件组件之间的通信时序，数据的输入输出，反映系统的功能流程 与数据流程,通常由时序图和流程图表示。</span></p><figure data-tool=\"mdnice编辑器\" style=\"margin-top: 10px;margin-bottom: 10px;margin-left: 0px;margin-right: 0px;padding-top: 0px;padding-bottom: 0px;padding-left: 0px;padding-right: 0px;display: flex;flex-direction: column;justify-content: center;align-items: center;\"><span leaf=\"\"><img data-src=\"https://mmbiz.qpic.cn/mmbiz_jpg/6mychickmupWDcvUWtYjTibVbFbG6N0LPJ3A4PBIziap4mqRmibxIgpS7KubpmiacmL5BVSVKiawRNPvpp6FDJlFTgxg/640?wx_fmt=other&amp;wxfrom=5&amp;wx_lazy=1&amp;wx_co=1&amp;tp=wxpic\" alt=\"图片\" class=\"rich_pages wxw-img\" data-ratio=\"0.8185185185185185\" data-w=\"1080\" style=\"display: block;margin-top: 0px;margin-right: auto;margin-bottom: 0px;margin-left: auto;max-width: 100%;border-top-style: none;border-bottom-style: none;border-left-style: none;border-right-style: none;border-top-width: 3px;border-bottom-width: 3px;border-left-width: 3px;border-right-width: 3px;border-top-color: rgba(0, 0, 0, 0.4);border-bottom-color: rgba(0, 0, 0, 0.4);border-left-color: rgba(0, 0, 0, 0.4);border-right-color: rgba(0, 0, 0, 0.4);border-top-left-radius: 4px;border-top-right-radius: 4px;border-bottom-right-radius: 4px;border-bottom-left-radius: 4px;object-fit: fill;box-shadow: rgba(0, 0, 0, 0) 0px 0px 0px 0px;\"></span><figcaption style=\"color: rgb(136, 136, 136);font-size: 12px;line-height: 1.5em;letter-spacing: 0em;text-align: center;font-weight: normal;margin-top: 5px;margin-bottom: 0px;margin-left: 0px;margin-right: 0px;padding-top: 0px;padding-bottom: 0px;padding-left: 0px;padding-right: 0px;\"><span leaf=\"\">图片</span></figcaption></figure><ul style=\"list-style-type: disc;margin-top: 8px;margin-bottom: 8px;margin-left: 0px;margin-right: 0px;padding-top: 0px;padding-bottom: 0px;padding-left: 25px;padding-right: 0px;color: rgb(0, 0, 0);\" class=\"list-paddingleft-1\"><li><section style=\"margin-top: 5px;margin-bottom: 5px;color: rgb(63, 63, 63);font-size: 16px;line-height: 1.8em;letter-spacing: 0.02em;text-align: left;font-weight: normal;\"><span leaf=\"\">开发视图</span></section></li></ul><p data-tool=\"mdnice编辑器\" style=\"color: rgb(63, 63, 63);font-size: 16px;line-height: 1.8em;letter-spacing: 0.02em;text-align: left;text-indent: 0em;margin-top: 0px;margin-bottom: 0px;margin-left: 0px;margin-right: 0px;padding-top: 16px;padding-bottom: 8px;padding-left: 0px;padding-right: 0px;\"><span leaf=\"\">开发视图用于描述系统的模块划分和组成，以及细化到内部包的组成设计，服务于开发人员，反映系统开发实施过程。</span></p><figure data-tool=\"mdnice编辑器\" style=\"margin-top: 10px;margin-bottom: 10px;margin-left: 0px;margin-right: 0px;padding-top: 0px;padding-bottom: 0px;padding-left: 0px;padding-right: 0px;display: flex;flex-direction: column;justify-content: center;align-items: center;\"><span leaf=\"\"><img data-src=\"https://mmbiz.qpic.cn/mmbiz_jpg/6mychickmupWDcvUWtYjTibVbFbG6N0LPJbO7BcS1neyk1oOobzsaicfRvl6NnaaQ7Gia2WoNqAlSDxXicWM4zDbgwQ/640?wx_fmt=other&amp;wxfrom=5&amp;wx_lazy=1&amp;wx_co=1&amp;tp=wxpic\" alt=\"图片\" class=\"rich_pages wxw-img\" data-ratio=\"0.7175925925925926\" data-w=\"1080\" style=\"display: block;margin-top: 0px;margin-right: auto;margin-bottom: 0px;margin-left: auto;max-width: 100%;border-top-style: none;border-bottom-style: none;border-left-style: none;border-right-style: none;border-top-width: 3px;border-bottom-width: 3px;border-left-width: 3px;border-right-width: 3px;border-top-color: rgba(0, 0, 0, 0.4);border-bottom-color: rgba(0, 0, 0, 0.4);border-left-color: rgba(0, 0, 0, 0.4);border-right-color: rgba(0, 0, 0, 0.4);border-top-left-radius: 4px;border-top-right-radius: 4px;border-bottom-right-radius: 4px;border-bottom-left-radius: 4px;object-fit: fill;box-shadow: rgba(0, 0, 0, 0) 0px 0px 0px 0px;\"></span><figcaption style=\"color: rgb(136, 136, 136);font-size: 12px;line-height: 1.5em;letter-spacing: 0em;text-align: center;font-weight: normal;margin-top: 5px;margin-bottom: 0px;margin-left: 0px;margin-right: 0px;padding-top: 0px;padding-bottom: 0px;padding-left: 0px;padding-right: 0px;\"><span leaf=\"\">图片</span></figcaption></figure><p data-tool=\"mdnice编辑器\" style=\"color: rgb(63, 63, 63);font-size: 16px;line-height: 1.8em;letter-spacing: 0.02em;text-align: left;text-indent: 0em;margin-top: 0px;margin-bottom: 0px;margin-left: 0px;margin-right: 0px;padding-top: 16px;padding-bottom: 8px;padding-left: 0px;padding-right: 0px;\"><span leaf=\"\">以上 5 种架构视图从不同角度表示一个软件系统的不同特征，组合到一起作为架构蓝图描述系统架构。</span></p><h2 data-cacheurl=\"\" data-remoteid=\"\" data-tool=\"mdnice编辑器\" style=\"margin-top: 30px;margin-bottom: 15px;margin-left: 0px;margin-right: 0px;padding-left: 0px;padding-right: 0px;background-attachment: scroll;background-clip: border-box;background-color: transparent;background-origin: padding-box;background-position-x: 50%;background-position-y: 50%;background-repeat: no-repeat;background-size: 63px;width: auto;height: auto;align-items: unset;border-top-style: none;border-bottom-style: none;border-left-style: none;border-right-style: none;border-top-width: 1px;border-bottom-width: 1px;border-left-width: 1px;border-right-width: 1px;border-top-color: rgb(0, 0, 0);border-bottom-color: rgb(0, 0, 0);border-left-color: rgb(0, 0, 0);border-right-color: rgb(0, 0, 0);border-top-left-radius: 0px;border-top-right-radius: 0px;border-bottom-left-radius: 0px;border-bottom-right-radius: 0px;box-shadow: none;display: flex;flex-direction: unset;float: unset;justify-content: center;line-height: 1.5em;overflow-x: unset;overflow-y: unset;padding-top: 0px;padding-bottom: 0px; text-align: left;text-shadow: none;transform: none;-webkit-box-reflect: unset;background-image: url(&quot;https://mmbiz.qpic.cn/sz_mmbiz_png/knmrNHnmCLE2wTd9jX9u0GWe2XtE2gG0Bs9yY5iagVWlfZlVX6Pj0NCx7WsN5oabG7Tc4J2CibjIWuTgUfeVcVVQ/640?wx_fmt=png&amp;from=appmsg&quot;);\"><span style=\"display: none;\"></span><span style=\" font-size: 18px;color: rgb(72, 179, 120);line-height: 2.4em;letter-spacing: 0em;margin-top: 38px;margin-bottom: 10px;border-top-style: none;border-bottom-style: none;border-left-style: none;border-right-style: none;border-top-width: 1px;border-bottom-width: 1px;border-left-width: 1px;border-right-width: 1px;border-top-color: rgb(0, 0, 0);border-bottom-color: rgb(0, 0, 0);border-left-color: rgb(0, 0, 0);border-right-color: rgb(0, 0, 0);border-top-left-radius: 0px;border-top-right-radius: 0px;border-bottom-left-radius: 0px;border-bottom-right-radius: 0px;align-items: unset;background-attachment: scroll;background-clip: border-box;background-color: transparent;background-image: none;background-origin: padding-box;background-position-x: 0%;background-position-y: 0%;background-repeat: no-repeat;background-size: auto;box-shadow: none;display: inline-block;font-weight: bold;flex-direction: unset;float: unset;height: 38px;justify-content: unset;margin-left: 0px;margin-right: 0px;overflow-x: unset;overflow-y: unset;padding-top: 0px;padding-bottom: 0px;padding-left: 0px;padding-right: 0px; text-align: center;text-indent: 0em;text-shadow: none;transform: none;width: auto;-webkit-box-reflect: unset; \"><strong style=\" align-items: unset;background-attachment: scroll;background-clip: border-box;background-color: transparent;background-image: none;background-origin: padding-box;background-position-x: 0%;background-position-y: 0%;background-repeat: no-repeat;background-size: auto;border-top-style: none;border-bottom-style: none;border-left-style: none;border-right-style: none;border-top-width: 1px;border-bottom-width: 1px;border-left-width: 1px;border-right-width: 1px;border-top-color: rgb(0, 0, 0);border-bottom-color: rgb(0, 0, 0);border-left-color: rgb(0, 0, 0);border-right-color: rgb(0, 0, 0);border-top-left-radius: 0px;border-top-right-radius: 0px;border-bottom-left-radius: 0px;border-bottom-right-radius: 0px;box-shadow: none;color: rgb(0, 0, 0);display: inline-block;font-size: 22px;font-weight: bold;flex-direction: unset;float: unset;height: auto;justify-content: unset;letter-spacing: 0px;line-height: 1.5em;margin-top: 0px;margin-bottom: 0px;margin-left: 0px;margin-right: 0px;overflow-x: unset;overflow-y: unset;padding-top: 0px;padding-bottom: 0px;padding-left: 0px;padding-right: 0px; text-align: left;text-indent: 0em;text-shadow: none;transform: none;width: auto;-webkit-box-reflect: unset; \"><span leaf=\"\">怎样的架构图是好的架构图</span></strong></span><span style=\"display: none;\"></span></h2><p data-tool=\"mdnice编辑器\" style=\"color: rgb(63, 63, 63);font-size: 16px;line-height: 1.8em;letter-spacing: 0.02em;text-align: left;text-indent: 0em;margin-top: 0px;margin-bottom: 0px;margin-left: 0px;margin-right: 0px;padding-top: 16px;padding-bottom: 8px;padding-left: 0px;padding-right: 0px;\"><span leaf=\"\">上面的分类是前人的经验总结，图也是从网上摘来的，那么这些图画的好不好呢？是不是我们要依葫芦画瓢去画这样一些图？</span></p><p data-tool=\"mdnice编辑器\" style=\"color: rgb(63, 63, 63);font-size: 16px;line-height: 1.8em;letter-spacing: 0.02em;text-align: left;text-indent: 0em;margin-top: 0px;margin-bottom: 0px;margin-left: 0px;margin-right: 0px;padding-top: 16px;padding-bottom: 8px;padding-left: 0px;padding-right: 0px;\"><span leaf=\"\">先不去管这些图好不好，我们通过对这些图的分类以及作用，思考了一下，总结下来，我们认为，在画出一个好的架构图之前， 首先应该要明确其受众，再想清楚要给他们传递什么信息 ，所以，不要为了画一个物理视图去画物理视图，为了画一个逻辑视图去画逻辑视图，而应该根据受众的不同，传递的信息的不同，用图准确地表达出来，最后的图可能就是在这样一些分类里。那么，画出的图好不好的一个直接标准就是：受众有没有准确接收到想传递的信息。</span></p><p data-tool=\"mdnice编辑器\" style=\"color: rgb(63, 63, 63);font-size: 16px;line-height: 1.8em;letter-spacing: 0.02em;text-align: left;text-indent: 0em;margin-top: 0px;margin-bottom: 0px;margin-left: 0px;margin-right: 0px;padding-top: 16px;padding-bottom: 8px;padding-left: 0px;padding-right: 0px;\"><span leaf=\"\">明确这两点之后，从受众角度来说，一个好的架构图是不需要解释的，它应该是自描述的，并且要具备一致性和足够的准确性，能够与代码相呼应。</span></p><p data-tool=\"mdnice编辑器\" style=\"color: rgb(63, 63, 63);font-size: 16px;line-height: 1.8em;letter-spacing: 0.02em;text-align: left;text-indent: 0em;margin-top: 0px;margin-bottom: 0px;margin-left: 0px;margin-right: 0px;padding-top: 16px;padding-bottom: 8px;padding-left: 0px;padding-right: 0px;\"><strong style=\"color: rgb(74, 74, 74);font-weight: bold;background-attachment: scroll;background-clip: border-box;background-color: rgba(0, 0, 0, 0);background-image: none;background-origin: padding-box;background-position-x: 0%;background-position-y: 0%;background-repeat: no-repeat;background-size: auto;width: auto;height: auto;margin-top: 0px;margin-bottom: 0px;margin-left: 0px;margin-right: 0px;padding-top: 0px;padding-bottom: 0px;padding-left: 0px;padding-right: 0px;border-top-style: none;border-bottom-style: none;border-left-style: none;border-right-style: none;border-top-width: 3px;border-bottom-width: 3px;border-left-width: 3px;border-right-width: 3px;border-top-color: rgba(0, 0, 0, 0.4);border-bottom-color: rgba(0, 0, 0, 0.4);border-left-color: rgba(0, 0, 0, 0.4);border-right-color: rgba(0, 0, 0, 0.4);border-top-left-radius: 0px;border-top-right-radius: 0px;border-bottom-right-radius: 0px;border-bottom-left-radius: 0px;\"><span leaf=\"\">画架构图遇到的常见问题</span></strong></p><h3 data-tool=\"mdnice编辑器\" style=\" margin-top: 30px;margin-bottom: 15px;margin-left: 0px;margin-right: 0px;padding-top: 0px;padding-bottom: 0px;padding-left: 0px;padding-right: 0px;align-items: unset;background-attachment: scroll;background-clip: border-box;background-color: transparent;background-image: none;background-origin: padding-box;background-position-x: 0%;background-position-y: 0%;background-repeat: no-repeat;background-size: auto;border-top-style: none;border-bottom-style: none;border-left-style: none;border-right-style: none;border-top-width: 1px;border-bottom-width: 1px;border-left-width: 1px;border-right-width: 1px;border-top-color: rgb(0, 0, 0);border-bottom-color: rgb(0, 0, 0);border-left-color: rgb(0, 0, 0);border-right-color: rgb(0, 0, 0);border-top-left-radius: 0px;border-top-right-radius: 0px;border-bottom-left-radius: 0px;border-bottom-right-radius: 0px;box-shadow: none;display: block;flex-direction: unset;float: unset;height: auto;justify-content: unset;line-height: 1.5em;overflow-x: unset;overflow-y: unset; text-align: left;text-shadow: none;transform: none;width: auto;-webkit-box-reflect: unset; \"><span data-cacheurl=\"\" data-remoteid=\"\" style=\"background-attachment: scroll;background-clip: border-box;background-color: transparent;background-origin: padding-box;background-position-x: 0%;background-position-y: 0%;background-repeat: no-repeat;background-size: 15px 15px;width: 15px;height: 15px;align-items: unset;border-top-style: none;border-bottom-style: none;border-left-style: none;border-right-style: none;border-top-width: 1px;border-bottom-width: 1px;border-left-width: 1px;border-right-width: 1px;border-top-color: rgb(0, 0, 0);border-bottom-color: rgb(0, 0, 0);border-left-color: rgb(0, 0, 0);border-right-color: rgb(0, 0, 0);border-top-left-radius: 0px;border-top-right-radius: 0px;border-bottom-left-radius: 0px;border-bottom-right-radius: 0px;box-shadow: none;color: rgb(0, 0, 0);display: inline-block;font-size: 22px;font-weight: bold;flex-direction: unset;float: unset;justify-content: unset;letter-spacing: 0px;line-height: 1.5em;margin-top: 0px;margin-bottom: -2px;margin-left: 0px;margin-right: 0px;overflow-x: unset;overflow-y: unset;padding-top: 0px;padding-bottom: 0px;padding-left: 0px;padding-right: 0px; text-align: left;text-indent: 0em;text-shadow: none;transform: none;-webkit-box-reflect: unset;background-image: url(&quot;https://mmbiz.qpic.cn/sz_mmbiz_png/knmrNHnmCLE2wTd9jX9u0GWe2XtE2gG0T83kl6QcwWG6uyzeibHmM8L1OBErtgTKOKFub6CcZrDE4RZCITBZTcQ/640?wx_fmt=png&amp;from=appmsg&quot;);\"></span><span style=\"display: none;\"></span><span style=\" font-size: 16px;color: rgb(72, 179, 120);line-height: 1.5em;letter-spacing: 0em;align-items: unset;background-attachment: scroll;background-clip: border-box;background-color: transparent;background-image: none;background-origin: padding-box;background-position-x: 0%;background-position-y: 0%;background-repeat: no-repeat;background-size: auto;border-top-style: none;border-bottom-style: none;border-left-style: none;border-right-style: none;border-top-width: 1px;border-bottom-width: 1px;border-left-width: 1px;border-right-width: 1px;border-top-color: rgb(0, 0, 0);border-bottom-color: rgb(0, 0, 0);border-left-color: rgb(0, 0, 0);border-right-color: rgb(0, 0, 0);border-top-left-radius: 0px;border-top-right-radius: 0px;border-bottom-left-radius: 0px;border-bottom-right-radius: 0px;box-shadow: none;display: inline-block;font-weight: bold;flex-direction: unset;float: unset;height: auto;justify-content: unset;margin-top: 0px;margin-bottom: 0px;margin-left: 8px;margin-right: 0px;overflow-x: unset;overflow-y: unset;padding-top: 0px;padding-bottom: 0px;padding-left: 0px;padding-right: 0px; text-align: left;text-indent: 0em;text-shadow: none;transform: none;width: auto;-webkit-box-reflect: unset; \"><span leaf=\"\">1、方框代表什么？</span></span><span style=\"display: none;\"></span></h3><figure data-tool=\"mdnice编辑器\" style=\"margin-top: 10px;margin-bottom: 10px;margin-left: 0px;margin-right: 0px;padding-top: 0px;padding-bottom: 0px;padding-left: 0px;padding-right: 0px;display: flex;flex-direction: column;justify-content: center;align-items: center;\"><span leaf=\"\"><img data-src=\"https://mmbiz.qpic.cn/mmbiz_jpg/6mychickmupWDcvUWtYjTibVbFbG6N0LPJBPYmunPvlm0w497lpeQh9b5YGjcZjPOgNCGsC2ibJribE2HG9Ht7icC6w/640?wx_fmt=other&amp;wxfrom=5&amp;wx_lazy=1&amp;wx_co=1&amp;tp=wxpic\" alt=\"图片\" class=\"rich_pages wxw-img\" data-ratio=\"0.2757009345794392\" data-w=\"428\" style=\"display: block;margin-top: 0px;margin-right: auto;margin-bottom: 0px;margin-left: auto;max-width: 100%;border-top-style: none;border-bottom-style: none;border-left-style: none;border-right-style: none;border-top-width: 3px;border-bottom-width: 3px;border-left-width: 3px;border-right-width: 3px;border-top-color: rgba(0, 0, 0, 0.4);border-bottom-color: rgba(0, 0, 0, 0.4);border-left-color: rgba(0, 0, 0, 0.4);border-right-color: rgba(0, 0, 0, 0.4);border-top-left-radius: 4px;border-top-right-radius: 4px;border-bottom-right-radius: 4px;border-bottom-left-radius: 4px;object-fit: fill;box-shadow: rgba(0, 0, 0, 0) 0px 0px 0px 0px;\"></span><figcaption style=\"color: rgb(136, 136, 136);font-size: 12px;line-height: 1.5em;letter-spacing: 0em;text-align: center;font-weight: normal;margin-top: 5px;margin-bottom: 0px;margin-left: 0px;margin-right: 0px;padding-top: 0px;padding-bottom: 0px;padding-left: 0px;padding-right: 0px;\"><span leaf=\"\">图片</span></figcaption></figure><p data-tool=\"mdnice编辑器\" style=\"color: rgb(63, 63, 63);font-size: 16px;line-height: 1.8em;letter-spacing: 0.02em;text-align: left;text-indent: 0em;margin-top: 0px;margin-bottom: 0px;margin-left: 0px;margin-right: 0px;padding-top: 16px;padding-bottom: 8px;padding-left: 0px;padding-right: 0px;\"><span leaf=\"\">为什么适用方框而不是圆形，它有什么特殊的含义吗？随意使用方框或者其它形状可能会引起混淆。</span></p><h3 data-tool=\"mdnice编辑器\" style=\" margin-top: 30px;margin-bottom: 15px;margin-left: 0px;margin-right: 0px;padding-top: 0px;padding-bottom: 0px;padding-left: 0px;padding-right: 0px;align-items: unset;background-attachment: scroll;background-clip: border-box;background-color: transparent;background-image: none;background-origin: padding-box;background-position-x: 0%;background-position-y: 0%;background-repeat: no-repeat;background-size: auto;border-top-style: none;border-bottom-style: none;border-left-style: none;border-right-style: none;border-top-width: 1px;border-bottom-width: 1px;border-left-width: 1px;border-right-width: 1px;border-top-color: rgb(0, 0, 0);border-bottom-color: rgb(0, 0, 0);border-left-color: rgb(0, 0, 0);border-right-color: rgb(0, 0, 0);border-top-left-radius: 0px;border-top-right-radius: 0px;border-bottom-left-radius: 0px;border-bottom-right-radius: 0px;box-shadow: none;display: block;flex-direction: unset;float: unset;height: auto;justify-content: unset;line-height: 1.5em;overflow-x: unset;overflow-y: unset; text-align: left;text-shadow: none;transform: none;width: auto;-webkit-box-reflect: unset; \"><span data-cacheurl=\"\" data-remoteid=\"\" style=\"background-attachment: scroll;background-clip: border-box;background-color: transparent;background-origin: padding-box;background-position-x: 0%;background-position-y: 0%;background-repeat: no-repeat;background-size: 15px 15px;width: 15px;height: 15px;align-items: unset;border-top-style: none;border-bottom-style: none;border-left-style: none;border-right-style: none;border-top-width: 1px;border-bottom-width: 1px;border-left-width: 1px;border-right-width: 1px;border-top-color: rgb(0, 0, 0);border-bottom-color: rgb(0, 0, 0);border-left-color: rgb(0, 0, 0);border-right-color: rgb(0, 0, 0);border-top-left-radius: 0px;border-top-right-radius: 0px;border-bottom-left-radius: 0px;border-bottom-right-radius: 0px;box-shadow: none;color: rgb(0, 0, 0);display: inline-block;font-size: 22px;font-weight: bold;flex-direction: unset;float: unset;justify-content: unset;letter-spacing: 0px;line-height: 1.5em;margin-top: 0px;margin-bottom: -2px;margin-left: 0px;margin-right: 0px;overflow-x: unset;overflow-y: unset;padding-top: 0px;padding-bottom: 0px;padding-left: 0px;padding-right: 0px; text-align: left;text-indent: 0em;text-shadow: none;transform: none;-webkit-box-reflect: unset;background-image: url(&quot;https://mmbiz.qpic.cn/sz_mmbiz_png/knmrNHnmCLE2wTd9jX9u0GWe2XtE2gG0T83kl6QcwWG6uyzeibHmM8L1OBErtgTKOKFub6CcZrDE4RZCITBZTcQ/640?wx_fmt=png&amp;from=appmsg&quot;);\"></span><span style=\"display: none;\"></span><span style=\" font-size: 16px;color: rgb(72, 179, 120);line-height: 1.5em;letter-spacing: 0em;align-items: unset;background-attachment: scroll;background-clip: border-box;background-color: transparent;background-image: none;background-origin: padding-box;background-position-x: 0%;background-position-y: 0%;background-repeat: no-repeat;background-size: auto;border-top-style: none;border-bottom-style: none;border-left-style: none;border-right-style: none;border-top-width: 1px;border-bottom-width: 1px;border-left-width: 1px;border-right-width: 1px;border-top-color: rgb(0, 0, 0);border-bottom-color: rgb(0, 0, 0);border-left-color: rgb(0, 0, 0);border-right-color: rgb(0, 0, 0);border-top-left-radius: 0px;border-top-right-radius: 0px;border-bottom-left-radius: 0px;border-bottom-right-radius: 0px;box-shadow: none;display: inline-block;font-weight: bold;flex-direction: unset;float: unset;height: auto;justify-content: unset;margin-top: 0px;margin-bottom: 0px;margin-left: 8px;margin-right: 0px;overflow-x: unset;overflow-y: unset;padding-top: 0px;padding-bottom: 0px;padding-left: 0px;padding-right: 0px; text-align: left;text-indent: 0em;text-shadow: none;transform: none;width: auto;-webkit-box-reflect: unset; \"><span leaf=\"\">2、虚线、实线什么意思？箭头什么意思？颜色什么意思？</span></span><span style=\"display: none;\"></span></h3><figure data-tool=\"mdnice编辑器\" style=\"margin-top: 10px;margin-bottom: 10px;margin-left: 0px;margin-right: 0px;padding-top: 0px;padding-bottom: 0px;padding-left: 0px;padding-right: 0px;display: flex;flex-direction: column;justify-content: center;align-items: center;\"><span leaf=\"\"><img alt=\"图片\" class=\"rich_pages wxw-img\" data-ratio=\"0.2\" data-src=\"https://mmbiz.qpic.cn/mmbiz_jpg/6mychickmupWDcvUWtYjTibVbFbG6N0LPJEIGkpOXkpbGpTicibucFT5sDYECV2xGibpH5gsiaSp2hyB6OsyD0Wbw4Lg/640?wx_fmt=other&amp;wxfrom=5&amp;wx_lazy=1&amp;wx_co=1&amp;tp=wxpic\" data-w=\"1080\" style=\"display: block;margin-top: 0px;margin-right: auto;margin-bottom: 0px;margin-left: auto;max-width: 100%;border-top-style: none;border-bottom-style: none;border-left-style: none;border-right-style: none;border-top-width: 3px;border-bottom-width: 3px;border-left-width: 3px;border-right-width: 3px;border-top-color: rgba(0, 0, 0, 0.4);border-bottom-color: rgba(0, 0, 0, 0.4);border-left-color: rgba(0, 0, 0, 0.4);border-right-color: rgba(0, 0, 0, 0.4);border-top-left-radius: 4px;border-top-right-radius: 4px;border-bottom-right-radius: 4px;border-bottom-left-radius: 4px;object-fit: fill;box-shadow: rgba(0, 0, 0, 0) 0px 0px 0px 0px;\"></span><figcaption style=\"color: rgb(136, 136, 136);font-size: 12px;line-height: 1.5em;letter-spacing: 0em;text-align: center;font-weight: normal;margin-top: 5px;margin-bottom: 0px;margin-left: 0px;margin-right: 0px;padding-top: 0px;padding-bottom: 0px;padding-left: 0px;padding-right: 0px;\"><span leaf=\"\">图片</span></figcaption></figure><p data-tool=\"mdnice编辑器\" style=\"color: rgb(63, 63, 63);font-size: 16px;line-height: 1.8em;letter-spacing: 0.02em;text-align: left;text-indent: 0em;margin-top: 0px;margin-bottom: 0px;margin-left: 0px;margin-right: 0px;padding-top: 16px;padding-bottom: 8px;padding-left: 0px;padding-right: 0px;\"><span leaf=\"\">随意使用线条或者箭头可能会引起误会。</span></p><h3 data-tool=\"mdnice编辑器\" style=\" margin-top: 30px;margin-bottom: 15px;margin-left: 0px;margin-right: 0px;padding-top: 0px;padding-bottom: 0px;padding-left: 0px;padding-right: 0px;align-items: unset;background-attachment: scroll;background-clip: border-box;background-color: transparent;background-image: none;background-origin: padding-box;background-position-x: 0%;background-position-y: 0%;background-repeat: no-repeat;background-size: auto;border-top-style: none;border-bottom-style: none;border-left-style: none;border-right-style: none;border-top-width: 1px;border-bottom-width: 1px;border-left-width: 1px;border-right-width: 1px;border-top-color: rgb(0, 0, 0);border-bottom-color: rgb(0, 0, 0);border-left-color: rgb(0, 0, 0);border-right-color: rgb(0, 0, 0);border-top-left-radius: 0px;border-top-right-radius: 0px;border-bottom-left-radius: 0px;border-bottom-right-radius: 0px;box-shadow: none;display: block;flex-direction: unset;float: unset;height: auto;justify-content: unset;line-height: 1.5em;overflow-x: unset;overflow-y: unset; text-align: left;text-shadow: none;transform: none;width: auto;-webkit-box-reflect: unset; \"><span data-cacheurl=\"\" data-remoteid=\"\" style=\"background-attachment: scroll;background-clip: border-box;background-color: transparent;background-origin: padding-box;background-position-x: 0%;background-position-y: 0%;background-repeat: no-repeat;background-size: 15px 15px;width: 15px;height: 15px;align-items: unset;border-top-style: none;border-bottom-style: none;border-left-style: none;border-right-style: none;border-top-width: 1px;border-bottom-width: 1px;border-left-width: 1px;border-right-width: 1px;border-top-color: rgb(0, 0, 0);border-bottom-color: rgb(0, 0, 0);border-left-color: rgb(0, 0, 0);border-right-color: rgb(0, 0, 0);border-top-left-radius: 0px;border-top-right-radius: 0px;border-bottom-left-radius: 0px;border-bottom-right-radius: 0px;box-shadow: none;color: rgb(0, 0, 0);display: inline-block;font-size: 22px;font-weight: bold;flex-direction: unset;float: unset;justify-content: unset;letter-spacing: 0px;line-height: 1.5em;margin-top: 0px;margin-bottom: -2px;margin-left: 0px;margin-right: 0px;overflow-x: unset;overflow-y: unset;padding-top: 0px;padding-bottom: 0px;padding-left: 0px;padding-right: 0px; text-align: left;text-indent: 0em;text-shadow: none;transform: none;-webkit-box-reflect: unset;background-image: url(&quot;https://mmbiz.qpic.cn/sz_mmbiz_png/knmrNHnmCLE2wTd9jX9u0GWe2XtE2gG0T83kl6QcwWG6uyzeibHmM8L1OBErtgTKOKFub6CcZrDE4RZCITBZTcQ/640?wx_fmt=png&amp;from=appmsg&quot;);\"></span><span style=\"display: none;\"></span><span style=\" font-size: 16px;color: rgb(72, 179, 120);line-height: 1.5em;letter-spacing: 0em;align-items: unset;background-attachment: scroll;background-clip: border-box;background-color: transparent;background-image: none;background-origin: padding-box;background-position-x: 0%;background-position-y: 0%;background-repeat: no-repeat;background-size: auto;border-top-style: none;border-bottom-style: none;border-left-style: none;border-right-style: none;border-top-width: 1px;border-bottom-width: 1px;border-left-width: 1px;border-right-width: 1px;border-top-color: rgb(0, 0, 0);border-bottom-color: rgb(0, 0, 0);border-left-color: rgb(0, 0, 0);border-right-color: rgb(0, 0, 0);border-top-left-radius: 0px;border-top-right-radius: 0px;border-bottom-left-radius: 0px;border-bottom-right-radius: 0px;box-shadow: none;display: inline-block;font-weight: bold;flex-direction: unset;float: unset;height: auto;justify-content: unset;margin-top: 0px;margin-bottom: 0px;margin-left: 8px;margin-right: 0px;overflow-x: unset;overflow-y: unset;padding-top: 0px;padding-bottom: 0px;padding-left: 0px;padding-right: 0px; text-align: left;text-indent: 0em;text-shadow: none;transform: none;width: auto;-webkit-box-reflect: unset; \"><span leaf=\"\">3、运行时与编译时冲突？层级冲突？</span></span><span style=\"display: none;\"></span></h3><figure data-tool=\"mdnice编辑器\" style=\"margin-top: 10px;margin-bottom: 10px;margin-left: 0px;margin-right: 0px;padding-top: 0px;padding-bottom: 0px;padding-left: 0px;padding-right: 0px;display: flex;flex-direction: column;justify-content: center;align-items: center;\"><span leaf=\"\"><img alt=\"图片\" class=\"rich_pages wxw-img\" data-ratio=\"0.2\" data-src=\"https://mmbiz.qpic.cn/mmbiz_jpg/6mychickmupWDcvUWtYjTibVbFbG6N0LPJEIGkpOXkpbGpTicibucFT5sDYECV2xGibpH5gsiaSp2hyB6OsyD0Wbw4Lg/640?wx_fmt=other&amp;wxfrom=5&amp;wx_lazy=1&amp;wx_co=1&amp;tp=wxpic\" data-w=\"1080\" style=\"display: block;margin-top: 0px;margin-right: auto;margin-bottom: 0px;margin-left: auto;max-width: 100%;border-top-style: none;border-bottom-style: none;border-left-style: none;border-right-style: none;border-top-width: 3px;border-bottom-width: 3px;border-left-width: 3px;border-right-width: 3px;border-top-color: rgba(0, 0, 0, 0.4);border-bottom-color: rgba(0, 0, 0, 0.4);border-left-color: rgba(0, 0, 0, 0.4);border-right-color: rgba(0, 0, 0, 0.4);border-top-left-radius: 4px;border-top-right-radius: 4px;border-bottom-right-radius: 4px;border-bottom-left-radius: 4px;object-fit: fill;box-shadow: rgba(0, 0, 0, 0) 0px 0px 0px 0px;\"></span><figcaption style=\"color: rgb(136, 136, 136);font-size: 12px;line-height: 1.5em;letter-spacing: 0em;text-align: center;font-weight: normal;margin-top: 5px;margin-bottom: 0px;margin-left: 0px;margin-right: 0px;padding-top: 0px;padding-bottom: 0px;padding-left: 0px;padding-right: 0px;\"><span leaf=\"\">图片</span></figcaption></figure><p data-tool=\"mdnice编辑器\" style=\"color: rgb(63, 63, 63);font-size: 16px;line-height: 1.8em;letter-spacing: 0.02em;text-align: left;text-indent: 0em;margin-top: 0px;margin-bottom: 0px;margin-left: 0px;margin-right: 0px;padding-top: 16px;padding-bottom: 8px;padding-left: 0px;padding-right: 0px;\"><span leaf=\"\">架构是一项复杂的工作，只使用单个图表来表示架构很容易造成莫名其妙的语义混乱。</span></p><h2 data-cacheurl=\"\" data-remoteid=\"\" data-tool=\"mdnice编辑器\" style=\"margin-top: 30px;margin-bottom: 15px;margin-left: 0px;margin-right: 0px;padding-left: 0px;padding-right: 0px;background-attachment: scroll;background-clip: border-box;background-color: transparent;background-origin: padding-box;background-position-x: 50%;background-position-y: 50%;background-repeat: no-repeat;background-size: 63px;width: auto;height: auto;align-items: unset;border-top-style: none;border-bottom-style: none;border-left-style: none;border-right-style: none;border-top-width: 1px;border-bottom-width: 1px;border-left-width: 1px;border-right-width: 1px;border-top-color: rgb(0, 0, 0);border-bottom-color: rgb(0, 0, 0);border-left-color: rgb(0, 0, 0);border-right-color: rgb(0, 0, 0);border-top-left-radius: 0px;border-top-right-radius: 0px;border-bottom-left-radius: 0px;border-bottom-right-radius: 0px;box-shadow: none;display: flex;flex-direction: unset;float: unset;justify-content: center;line-height: 1.5em;overflow-x: unset;overflow-y: unset;padding-top: 0px;padding-bottom: 0px; text-align: left;text-shadow: none;transform: none;-webkit-box-reflect: unset;background-image: url(&quot;https://mmbiz.qpic.cn/sz_mmbiz_png/knmrNHnmCLE2wTd9jX9u0GWe2XtE2gG0Bs9yY5iagVWlfZlVX6Pj0NCx7WsN5oabG7Tc4J2CibjIWuTgUfeVcVVQ/640?wx_fmt=png&amp;from=appmsg&quot;);\"><span style=\"display: none;\"></span><span style=\" font-size: 18px;color: rgb(72, 179, 120);line-height: 2.4em;letter-spacing: 0em;margin-top: 38px;margin-bottom: 10px;border-top-style: none;border-bottom-style: none;border-left-style: none;border-right-style: none;border-top-width: 1px;border-bottom-width: 1px;border-left-width: 1px;border-right-width: 1px;border-top-color: rgb(0, 0, 0);border-bottom-color: rgb(0, 0, 0);border-left-color: rgb(0, 0, 0);border-right-color: rgb(0, 0, 0);border-top-left-radius: 0px;border-top-right-radius: 0px;border-bottom-left-radius: 0px;border-bottom-right-radius: 0px;align-items: unset;background-attachment: scroll;background-clip: border-box;background-color: transparent;background-image: none;background-origin: padding-box;background-position-x: 0%;background-position-y: 0%;background-repeat: no-repeat;background-size: auto;box-shadow: none;display: inline-block;font-weight: bold;flex-direction: unset;float: unset;height: 38px;justify-content: unset;margin-left: 0px;margin-right: 0px;overflow-x: unset;overflow-y: unset;padding-top: 0px;padding-bottom: 0px;padding-left: 0px;padding-right: 0px; text-align: center;text-indent: 0em;text-shadow: none;transform: none;width: auto;-webkit-box-reflect: unset; \"><strong style=\" align-items: unset;background-attachment: scroll;background-clip: border-box;background-color: transparent;background-image: none;background-origin: padding-box;background-position-x: 0%;background-position-y: 0%;background-repeat: no-repeat;background-size: auto;border-top-style: none;border-bottom-style: none;border-left-style: none;border-right-style: none;border-top-width: 1px;border-bottom-width: 1px;border-left-width: 1px;border-right-width: 1px;border-top-color: rgb(0, 0, 0);border-bottom-color: rgb(0, 0, 0);border-left-color: rgb(0, 0, 0);border-right-color: rgb(0, 0, 0);border-top-left-radius: 0px;border-top-right-radius: 0px;border-bottom-left-radius: 0px;border-bottom-right-radius: 0px;box-shadow: none;color: rgb(0, 0, 0);display: inline-block;font-size: 22px;font-weight: bold;flex-direction: unset;float: unset;height: auto;justify-content: unset;letter-spacing: 0px;line-height: 1.5em;margin-top: 0px;margin-bottom: 0px;margin-left: 0px;margin-right: 0px;overflow-x: unset;overflow-y: unset;padding-top: 0px;padding-bottom: 0px;padding-left: 0px;padding-right: 0px; text-align: left;text-indent: 0em;text-shadow: none;transform: none;width: auto;-webkit-box-reflect: unset; \"><span leaf=\"\">本文推荐的画图方法</span></strong></span><span style=\"display: none;\"></span></h2><figure data-tool=\"mdnice编辑器\" style=\"margin-top: 10px;margin-bottom: 10px;margin-left: 0px;margin-right: 0px;padding-top: 0px;padding-bottom: 0px;padding-left: 0px;padding-right: 0px;display: flex;flex-direction: column;justify-content: center;align-items: center;\"><span leaf=\"\"><img alt=\"图片\" class=\"rich_pages wxw-img\" data-ratio=\"0.5614457831325301\" data-src=\"https://mmbiz.qpic.cn/mmbiz_jpg/6mychickmupWDcvUWtYjTibVbFbG6N0LPJvJ7icqcP97ciadAYhdJ3YDPxXTM4OUjzlwtfQ7PiaWOaER9acU2Mho2bg/640?wx_fmt=other&amp;wxfrom=5&amp;wx_lazy=1&amp;wx_co=1&amp;tp=wxpic\" data-w=\"830\" style=\"display: block;margin-top: 0px;margin-right: auto;margin-bottom: 0px;margin-left: auto;max-width: 100%;border-top-style: none;border-bottom-style: none;border-left-style: none;border-right-style: none;border-top-width: 3px;border-bottom-width: 3px;border-left-width: 3px;border-right-width: 3px;border-top-color: rgba(0, 0, 0, 0.4);border-bottom-color: rgba(0, 0, 0, 0.4);border-left-color: rgba(0, 0, 0, 0.4);border-right-color: rgba(0, 0, 0, 0.4);border-top-left-radius: 4px;border-top-right-radius: 4px;border-bottom-right-radius: 4px;border-bottom-left-radius: 4px;object-fit: fill;box-shadow: rgba(0, 0, 0, 0) 0px 0px 0px 0px;\"></span><figcaption style=\"color: rgb(136, 136, 136);font-size: 12px;line-height: 1.5em;letter-spacing: 0em;text-align: center;font-weight: normal;margin-top: 5px;margin-bottom: 0px;margin-left: 0px;margin-right: 0px;padding-top: 0px;padding-bottom: 0px;padding-left: 0px;padding-right: 0px;\"><span leaf=\"\">图片</span></figcaption></figure><p data-tool=\"mdnice编辑器\" style=\"color: rgb(63, 63, 63);font-size: 16px;line-height: 1.8em;letter-spacing: 0.02em;text-align: left;text-indent: 0em;margin-top: 0px;margin-bottom: 0px;margin-left: 0px;margin-right: 0px;padding-top: 16px;padding-bottom: 8px;padding-left: 0px;padding-right: 0px;\"><span leaf=\"\">C4 模型使用容器（应用程序、数据存储、微服务等）、组件和代码来描述一个软件系统的静态结构。这几种图比较容易画，也给出了画图要点，但最关键的是，我们认为，它明确指出了每种图可能的受众以及意义。</span></p><p data-tool=\"mdnice编辑器\" style=\"color: rgb(63, 63, 63);font-size: 16px;line-height: 1.8em;letter-spacing: 0.02em;text-align: left;text-indent: 0em;margin-top: 0px;margin-bottom: 0px;margin-left: 0px;margin-right: 0px;padding-top: 16px;padding-bottom: 8px;padding-left: 0px;padding-right: 0px;\"><span leaf=\"\">下面的案例来自C4官网，然后加上了一些我们的理解，来看看如何更好的表达软件架构</span></p><h3 data-tool=\"mdnice编辑器\" style=\" margin-top: 30px;margin-bottom: 15px;margin-left: 0px;margin-right: 0px;padding-top: 0px;padding-bottom: 0px;padding-left: 0px;padding-right: 0px;align-items: unset;background-attachment: scroll;background-clip: border-box;background-color: transparent;background-image: none;background-origin: padding-box;background-position-x: 0%;background-position-y: 0%;background-repeat: no-repeat;background-size: auto;border-top-style: none;border-bottom-style: none;border-left-style: none;border-right-style: none;border-top-width: 1px;border-bottom-width: 1px;border-left-width: 1px;border-right-width: 1px;border-top-color: rgb(0, 0, 0);border-bottom-color: rgb(0, 0, 0);border-left-color: rgb(0, 0, 0);border-right-color: rgb(0, 0, 0);border-top-left-radius: 0px;border-top-right-radius: 0px;border-bottom-left-radius: 0px;border-bottom-right-radius: 0px;box-shadow: none;display: block;flex-direction: unset;float: unset;height: auto;justify-content: unset;line-height: 1.5em;overflow-x: unset;overflow-y: unset; text-align: left;text-shadow: none;transform: none;width: auto;-webkit-box-reflect: unset; \"><span data-cacheurl=\"\" data-remoteid=\"\" style=\"background-attachment: scroll;background-clip: border-box;background-color: transparent;background-origin: padding-box;background-position-x: 0%;background-position-y: 0%;background-repeat: no-repeat;background-size: 15px 15px;width: 15px;height: 15px;align-items: unset;border-top-style: none;border-bottom-style: none;border-left-style: none;border-right-style: none;border-top-width: 1px;border-bottom-width: 1px;border-left-width: 1px;border-right-width: 1px;border-top-color: rgb(0, 0, 0);border-bottom-color: rgb(0, 0, 0);border-left-color: rgb(0, 0, 0);border-right-color: rgb(0, 0, 0);border-top-left-radius: 0px;border-top-right-radius: 0px;border-bottom-left-radius: 0px;border-bottom-right-radius: 0px;box-shadow: none;color: rgb(0, 0, 0);display: inline-block;font-size: 22px;font-weight: bold;flex-direction: unset;float: unset;justify-content: unset;letter-spacing: 0px;line-height: 1.5em;margin-top: 0px;margin-bottom: -2px;margin-left: 0px;margin-right: 0px;overflow-x: unset;overflow-y: unset;padding-top: 0px;padding-bottom: 0px;padding-left: 0px;padding-right: 0px; text-align: left;text-indent: 0em;text-shadow: none;transform: none;-webkit-box-reflect: unset;background-image: url(&quot;https://mmbiz.qpic.cn/sz_mmbiz_png/knmrNHnmCLE2wTd9jX9u0GWe2XtE2gG0T83kl6QcwWG6uyzeibHmM8L1OBErtgTKOKFub6CcZrDE4RZCITBZTcQ/640?wx_fmt=png&amp;from=appmsg&quot;);\"></span><span style=\"display: none;\"></span><span style=\" font-size: 16px;color: rgb(72, 179, 120);line-height: 1.5em;letter-spacing: 0em;align-items: unset;background-attachment: scroll;background-clip: border-box;background-color: transparent;background-image: none;background-origin: padding-box;background-position-x: 0%;background-position-y: 0%;background-repeat: no-repeat;background-size: auto;border-top-style: none;border-bottom-style: none;border-left-style: none;border-right-style: none;border-top-width: 1px;border-bottom-width: 1px;border-left-width: 1px;border-right-width: 1px;border-top-color: rgb(0, 0, 0);border-bottom-color: rgb(0, 0, 0);border-left-color: rgb(0, 0, 0);border-right-color: rgb(0, 0, 0);border-top-left-radius: 0px;border-top-right-radius: 0px;border-bottom-left-radius: 0px;border-bottom-right-radius: 0px;box-shadow: none;display: inline-block;font-weight: bold;flex-direction: unset;float: unset;height: auto;justify-content: unset;margin-top: 0px;margin-bottom: 0px;margin-left: 8px;margin-right: 0px;overflow-x: unset;overflow-y: unset;padding-top: 0px;padding-bottom: 0px;padding-left: 0px;padding-right: 0px; text-align: left;text-indent: 0em;text-shadow: none;transform: none;width: auto;-webkit-box-reflect: unset; \"><span leaf=\"\">1、语境图(System Context Diagram)</span></span><span style=\"display: none;\"></span></h3><figure data-tool=\"mdnice编辑器\" style=\"margin-top: 10px;margin-bottom: 10px;margin-left: 0px;margin-right: 0px;padding-top: 0px;padding-bottom: 0px;padding-left: 0px;padding-right: 0px;display: flex;flex-direction: column;justify-content: center;align-items: center;\"><span leaf=\"\"><img alt=\"图片\" class=\"rich_pages wxw-img\" data-ratio=\"0.7947019867549668\" data-src=\"https://mmbiz.qpic.cn/mmbiz_jpg/6mychickmupWDcvUWtYjTibVbFbG6N0LPJVcLXdujNYG0QFMxgYfhorstaXJX7ut6P9575NnAYqiaktzWxp7PHD6g/640?wx_fmt=other&amp;wxfrom=5&amp;wx_lazy=1&amp;wx_co=1&amp;tp=wxpic\" data-w=\"906\" style=\"display: block;margin-top: 0px;margin-right: auto;margin-bottom: 0px;margin-left: auto;max-width: 100%;border-top-style: none;border-bottom-style: none;border-left-style: none;border-right-style: none;border-top-width: 3px;border-bottom-width: 3px;border-left-width: 3px;border-right-width: 3px;border-top-color: rgba(0, 0, 0, 0.4);border-bottom-color: rgba(0, 0, 0, 0.4);border-left-color: rgba(0, 0, 0, 0.4);border-right-color: rgba(0, 0, 0, 0.4);border-top-left-radius: 4px;border-top-right-radius: 4px;border-bottom-right-radius: 4px;border-bottom-left-radius: 4px;object-fit: fill;box-shadow: rgba(0, 0, 0, 0) 0px 0px 0px 0px;\"></span><figcaption style=\"color: rgb(136, 136, 136);font-size: 12px;line-height: 1.5em;letter-spacing: 0em;text-align: center;font-weight: normal;margin-top: 5px;margin-bottom: 0px;margin-left: 0px;margin-right: 0px;padding-top: 0px;padding-bottom: 0px;padding-left: 0px;padding-right: 0px;\"><span leaf=\"\">图片</span></figcaption></figure><p data-tool=\"mdnice编辑器\" style=\"color: rgb(63, 63, 63);font-size: 16px;line-height: 1.8em;letter-spacing: 0.02em;text-align: left;text-indent: 0em;margin-top: 0px;margin-bottom: 0px;margin-left: 0px;margin-right: 0px;padding-top: 16px;padding-bottom: 8px;padding-left: 0px;padding-right: 0px;\"><span leaf=\"\">这是一个想象的待建设的互联网银行系统，它使用外部的大型机银行系统存取客户账户、交易信息，通过外部电邮系统给客户发邮件。可以看到，非常简单、清晰，相信不需要解释，都看的明白，里面包含了需要建设的系统本身，系统的客户，和这个系统有交互的周边系统。</span></p><p data-tool=\"mdnice编辑器\" style=\"color: rgb(63, 63, 63);font-size: 16px;line-height: 1.8em;letter-spacing: 0.02em;text-align: left;text-indent: 0em;margin-top: 0px;margin-bottom: 0px;margin-left: 0px;margin-right: 0px;padding-top: 16px;padding-bottom: 8px;padding-left: 0px;padding-right: 0px;\"><em style=\"color: rgb(0, 0, 0);font-style: italic;background-attachment: scroll;background-clip: border-box;background-color: rgba(0, 0, 0, 0);background-image: none;background-origin: padding-box;background-position-x: 0%;background-position-y: 0%;background-repeat: no-repeat;background-size: auto;width: auto;height: auto;margin-top: 0px;margin-bottom: 0px;margin-left: 0px;margin-right: 0px;padding-top: 0px;padding-bottom: 0px;padding-left: 0px;padding-right: 0px;border-top-style: none;border-bottom-style: none;border-left-style: none;border-right-style: none;border-top-width: 3px;border-bottom-width: 3px;border-left-width: 3px;border-right-width: 3px;border-top-color: rgba(0, 0, 0, 0.4);border-bottom-color: rgba(0, 0, 0, 0.4);border-left-color: rgba(0, 0, 0, 0.4);border-right-color: rgba(0, 0, 0, 0.4);border-top-left-radius: 0px;border-top-right-radius: 0px;border-bottom-right-radius: 0px;border-bottom-left-radius: 0px;\"><span leaf=\"\">用途</span></em></p><p data-tool=\"mdnice编辑器\" style=\"color: rgb(63, 63, 63);font-size: 16px;line-height: 1.8em;letter-spacing: 0.02em;text-align: left;text-indent: 0em;margin-top: 0px;margin-bottom: 0px;margin-left: 0px;margin-right: 0px;padding-top: 16px;padding-bottom: 8px;padding-left: 0px;padding-right: 0px;\"><span leaf=\"\">这样一个简单的图，可以告诉我们，要构建的系统是什么；它的用户是谁，谁会用它，它要如何融入已有的IT环境。这个图的受众可以是开发团队的内部人员、外部的技术或非技术人员。即：</span></p><ul style=\"list-style-type: disc;margin-top: 8px;margin-bottom: 8px;margin-left: 0px;margin-right: 0px;padding-top: 0px;padding-bottom: 0px;padding-left: 25px;padding-right: 0px;color: rgb(0, 0, 0);\" class=\"list-paddingleft-1\"><li><section style=\"margin-top: 5px;margin-bottom: 5px;color: rgb(63, 63, 63);font-size: 16px;line-height: 1.8em;letter-spacing: 0.02em;text-align: left;font-weight: normal;\"><span leaf=\"\">构建的系统是什么</span></section></li><li><section style=\"margin-top: 5px;margin-bottom: 5px;color: rgb(63, 63, 63);font-size: 16px;line-height: 1.8em;letter-spacing: 0.02em;text-align: left;font-weight: normal;\"><span leaf=\"\">谁会用它</span></section></li><li><section style=\"margin-top: 5px;margin-bottom: 5px;color: rgb(63, 63, 63);font-size: 16px;line-height: 1.8em;letter-spacing: 0.02em;text-align: left;font-weight: normal;\"><span leaf=\"\">如何融入已有的IT环境</span></section></li></ul><p data-tool=\"mdnice编辑器\" style=\"color: rgb(63, 63, 63);font-size: 16px;line-height: 1.8em;letter-spacing: 0.02em;text-align: left;text-indent: 0em;margin-top: 0px;margin-bottom: 0px;margin-left: 0px;margin-right: 0px;padding-top: 16px;padding-bottom: 8px;padding-left: 0px;padding-right: 0px;\"><em style=\"color: rgb(0, 0, 0);font-style: italic;background-attachment: scroll;background-clip: border-box;background-color: rgba(0, 0, 0, 0);background-image: none;background-origin: padding-box;background-position-x: 0%;background-position-y: 0%;background-repeat: no-repeat;background-size: auto;width: auto;height: auto;margin-top: 0px;margin-bottom: 0px;margin-left: 0px;margin-right: 0px;padding-top: 0px;padding-bottom: 0px;padding-left: 0px;padding-right: 0px;border-top-style: none;border-bottom-style: none;border-left-style: none;border-right-style: none;border-top-width: 3px;border-bottom-width: 3px;border-left-width: 3px;border-right-width: 3px;border-top-color: rgba(0, 0, 0, 0.4);border-bottom-color: rgba(0, 0, 0, 0.4);border-left-color: rgba(0, 0, 0, 0.4);border-right-color: rgba(0, 0, 0, 0.4);border-top-left-radius: 0px;border-top-right-radius: 0px;border-bottom-right-radius: 0px;border-bottom-left-radius: 0px;\"><span leaf=\"\">怎么画</span></em></p><p data-tool=\"mdnice编辑器\" style=\"color: rgb(63, 63, 63);font-size: 16px;line-height: 1.8em;letter-spacing: 0.02em;text-align: left;text-indent: 0em;margin-top: 0px;margin-bottom: 0px;margin-left: 0px;margin-right: 0px;padding-top: 16px;padding-bottom: 8px;padding-left: 0px;padding-right: 0px;\"><span leaf=\"\">中间是自己的系统，周围是用户和其它与之相互作用的系统。这个图的关键就是梳理清楚待建设系统的用户和高层次的依赖，梳理清楚了画下来只需要几分钟时间。</span></p><h3 data-tool=\"mdnice编辑器\" style=\" margin-top: 30px;margin-bottom: 15px;margin-left: 0px;margin-right: 0px;padding-top: 0px;padding-bottom: 0px;padding-left: 0px;padding-right: 0px;align-items: unset;background-attachment: scroll;background-clip: border-box;background-color: transparent;background-image: none;background-origin: padding-box;background-position-x: 0%;background-position-y: 0%;background-repeat: no-repeat;background-size: auto;border-top-style: none;border-bottom-style: none;border-left-style: none;border-right-style: none;border-top-width: 1px;border-bottom-width: 1px;border-left-width: 1px;border-right-width: 1px;border-top-color: rgb(0, 0, 0);border-bottom-color: rgb(0, 0, 0);border-left-color: rgb(0, 0, 0);border-right-color: rgb(0, 0, 0);border-top-left-radius: 0px;border-top-right-radius: 0px;border-bottom-left-radius: 0px;border-bottom-right-radius: 0px;box-shadow: none;display: block;flex-direction: unset;float: unset;height: auto;justify-content: unset;line-height: 1.5em;overflow-x: unset;overflow-y: unset; text-align: left;text-shadow: none;transform: none;width: auto;-webkit-box-reflect: unset; \"><span data-cacheurl=\"\" data-remoteid=\"\" style=\"background-attachment: scroll;background-clip: border-box;background-color: transparent;background-origin: padding-box;background-position-x: 0%;background-position-y: 0%;background-repeat: no-repeat;background-size: 15px 15px;width: 15px;height: 15px;align-items: unset;border-top-style: none;border-bottom-style: none;border-left-style: none;border-right-style: none;border-top-width: 1px;border-bottom-width: 1px;border-left-width: 1px;border-right-width: 1px;border-top-color: rgb(0, 0, 0);border-bottom-color: rgb(0, 0, 0);border-left-color: rgb(0, 0, 0);border-right-color: rgb(0, 0, 0);border-top-left-radius: 0px;border-top-right-radius: 0px;border-bottom-left-radius: 0px;border-bottom-right-radius: 0px;box-shadow: none;color: rgb(0, 0, 0);display: inline-block;font-size: 22px;font-weight: bold;flex-direction: unset;float: unset;justify-content: unset;letter-spacing: 0px;line-height: 1.5em;margin-top: 0px;margin-bottom: -2px;margin-left: 0px;margin-right: 0px;overflow-x: unset;overflow-y: unset;padding-top: 0px;padding-bottom: 0px;padding-left: 0px;padding-right: 0px; text-align: left;text-indent: 0em;text-shadow: none;transform: none;-webkit-box-reflect: unset;background-image: url(&quot;https://mmbiz.qpic.cn/sz_mmbiz_png/knmrNHnmCLE2wTd9jX9u0GWe2XtE2gG0T83kl6QcwWG6uyzeibHmM8L1OBErtgTKOKFub6CcZrDE4RZCITBZTcQ/640?wx_fmt=png&amp;from=appmsg&quot;);\"></span><span style=\"display: none;\"></span><span style=\" font-size: 16px;color: rgb(72, 179, 120);line-height: 1.5em;letter-spacing: 0em;align-items: unset;background-attachment: scroll;background-clip: border-box;background-color: transparent;background-image: none;background-origin: padding-box;background-position-x: 0%;background-position-y: 0%;background-repeat: no-repeat;background-size: auto;border-top-style: none;border-bottom-style: none;border-left-style: none;border-right-style: none;border-top-width: 1px;border-bottom-width: 1px;border-left-width: 1px;border-right-width: 1px;border-top-color: rgb(0, 0, 0);border-bottom-color: rgb(0, 0, 0);border-left-color: rgb(0, 0, 0);border-right-color: rgb(0, 0, 0);border-top-left-radius: 0px;border-top-right-radius: 0px;border-bottom-left-radius: 0px;border-bottom-right-radius: 0px;box-shadow: none;display: inline-block;font-weight: bold;flex-direction: unset;float: unset;height: auto;justify-content: unset;margin-top: 0px;margin-bottom: 0px;margin-left: 8px;margin-right: 0px;overflow-x: unset;overflow-y: unset;padding-top: 0px;padding-bottom: 0px;padding-left: 0px;padding-right: 0px; text-align: left;text-indent: 0em;text-shadow: none;transform: none;width: auto;-webkit-box-reflect: unset; \"><span leaf=\"\">2、容器图(Container Diagram)</span></span><span style=\"display: none;\"></span></h3><p data-tool=\"mdnice编辑器\" style=\"color: rgb(63, 63, 63);font-size: 16px;line-height: 1.8em;letter-spacing: 0.02em;text-align: left;text-indent: 0em;margin-top: 0px;margin-bottom: 0px;margin-left: 0px;margin-right: 0px;padding-top: 16px;padding-bottom: 8px;padding-left: 0px;padding-right: 0px;\"><span leaf=\"\">容器图是把语境图里待建设的系统做了一个展开。</span></p><figure data-tool=\"mdnice编辑器\" style=\"margin-top: 10px;margin-bottom: 10px;margin-left: 0px;margin-right: 0px;padding-top: 0px;padding-bottom: 0px;padding-left: 0px;padding-right: 0px;display: flex;flex-direction: column;justify-content: center;align-items: center;\"><span leaf=\"\"><img alt=\"图片\" class=\"rich_pages wxw-img\" data-ratio=\"0.8546296296296296\" data-src=\"https://mmbiz.qpic.cn/mmbiz_jpg/6mychickmupWDcvUWtYjTibVbFbG6N0LPJj9peG62ic4QVFSsh3yzDpWl0OIYbVCicStJiaAaHiaeVzw9ePJecNMg7Eg/640?wx_fmt=other&amp;wxfrom=5&amp;wx_lazy=1&amp;wx_co=1&amp;tp=wxpic\" data-w=\"1080\" style=\"display: block;margin-top: 0px;margin-right: auto;margin-bottom: 0px;margin-left: auto;max-width: 100%;border-top-style: none;border-bottom-style: none;border-left-style: none;border-right-style: none;border-top-width: 3px;border-bottom-width: 3px;border-left-width: 3px;border-right-width: 3px;border-top-color: rgba(0, 0, 0, 0.4);border-bottom-color: rgba(0, 0, 0, 0.4);border-left-color: rgba(0, 0, 0, 0.4);border-right-color: rgba(0, 0, 0, 0.4);border-top-left-radius: 4px;border-top-right-radius: 4px;border-bottom-right-radius: 4px;border-bottom-left-radius: 4px;object-fit: fill;box-shadow: rgba(0, 0, 0, 0) 0px 0px 0px 0px;\"></span><figcaption style=\"color: rgb(136, 136, 136);font-size: 12px;line-height: 1.5em;letter-spacing: 0em;text-align: center;font-weight: normal;margin-top: 5px;margin-bottom: 0px;margin-left: 0px;margin-right: 0px;padding-top: 0px;padding-bottom: 0px;padding-left: 0px;padding-right: 0px;\"><span leaf=\"\">图片</span></figcaption></figure><p data-tool=\"mdnice编辑器\" style=\"color: rgb(63, 63, 63);font-size: 16px;line-height: 1.8em;letter-spacing: 0.02em;text-align: left;text-indent: 0em;margin-top: 0px;margin-bottom: 0px;margin-left: 0px;margin-right: 0px;padding-top: 16px;padding-bottom: 8px;padding-left: 0px;padding-right: 0px;\"><span leaf=\"\">上图中，除了用户和外围系统，要建设的系统包括一个基于java\\spring mvc的web应用提供系统的功能入口，基于xamarin架构的手机app提供手机端的功能入口，一个基于java的api应用提供服务，一个mysql数据库用于存储，</span></p><p data-tool=\"mdnice编辑器\" style=\"color: rgb(63, 63, 63);font-size: 16px;line-height: 1.8em;letter-spacing: 0.02em;text-align: left;text-indent: 0em;margin-top: 0px;margin-bottom: 0px;margin-left: 0px;margin-right: 0px;padding-top: 16px;padding-bottom: 8px;padding-left: 0px;padding-right: 0px;\"><span leaf=\"\">各个应用之间的交互都在箭头线上写明了。</span></p><p data-tool=\"mdnice编辑器\" style=\"color: rgb(63, 63, 63);font-size: 16px;line-height: 1.8em;letter-spacing: 0.02em;text-align: left;text-indent: 0em;margin-top: 0px;margin-bottom: 0px;margin-left: 0px;margin-right: 0px;padding-top: 16px;padding-bottom: 8px;padding-left: 0px;padding-right: 0px;\"><span leaf=\"\">看这张图的时候，不会去关注到图中是直角方框还是圆角方框，不会关注是实线箭头还是虚线箭头，甚至箭头的指向也没有引起太多注意。</span></p><p data-tool=\"mdnice编辑器\" style=\"color: rgb(63, 63, 63);font-size: 16px;line-height: 1.8em;letter-spacing: 0.02em;text-align: left;text-indent: 0em;margin-top: 0px;margin-bottom: 0px;margin-left: 0px;margin-right: 0px;padding-top: 16px;padding-bottom: 8px;padding-left: 0px;padding-right: 0px;\"><span leaf=\"\">我们有许多的画图方式，都对框、线的含义做了定义，这就需要画图的人和看图的人都清晰的理解这些定义，才能读全图里的信息，而现实是，这往往是非常高的一个要求，所以，很多图只能看个大概的含义。</span></p><p data-tool=\"mdnice编辑器\" style=\"color: rgb(63, 63, 63);font-size: 16px;line-height: 1.8em;letter-spacing: 0.02em;text-align: left;text-indent: 0em;margin-top: 0px;margin-bottom: 0px;margin-left: 0px;margin-right: 0px;padding-top: 16px;padding-bottom: 8px;padding-left: 0px;padding-right: 0px;\"><em style=\"color: rgb(0, 0, 0);font-style: italic;background-attachment: scroll;background-clip: border-box;background-color: rgba(0, 0, 0, 0);background-image: none;background-origin: padding-box;background-position-x: 0%;background-position-y: 0%;background-repeat: no-repeat;background-size: auto;width: auto;height: auto;margin-top: 0px;margin-bottom: 0px;margin-left: 0px;margin-right: 0px;padding-top: 0px;padding-bottom: 0px;padding-left: 0px;padding-right: 0px;border-top-style: none;border-bottom-style: none;border-left-style: none;border-right-style: none;border-top-width: 3px;border-bottom-width: 3px;border-left-width: 3px;border-right-width: 3px;border-top-color: rgba(0, 0, 0, 0.4);border-bottom-color: rgba(0, 0, 0, 0.4);border-left-color: rgba(0, 0, 0, 0.4);border-right-color: rgba(0, 0, 0, 0.4);border-top-left-radius: 0px;border-top-right-radius: 0px;border-bottom-right-radius: 0px;border-bottom-left-radius: 0px;\"><span leaf=\"\">用途</span></em></p><p data-tool=\"mdnice编辑器\" style=\"color: rgb(63, 63, 63);font-size: 16px;line-height: 1.8em;letter-spacing: 0.02em;text-align: left;text-indent: 0em;margin-top: 0px;margin-bottom: 0px;margin-left: 0px;margin-right: 0px;padding-top: 16px;padding-bottom: 8px;padding-left: 0px;padding-right: 0px;\"><span leaf=\"\">这个图的受众可以是团队内部或外部的开发人员，也可以是运维人员。用途可以罗列为：</span></p><ul style=\"list-style-type: disc;margin-top: 8px;margin-bottom: 8px;margin-left: 0px;margin-right: 0px;padding-top: 0px;padding-bottom: 0px;padding-left: 25px;padding-right: 0px;color: rgb(0, 0, 0);\" class=\"list-paddingleft-1\"><li><section style=\"margin-top: 5px;margin-bottom: 5px;color: rgb(63, 63, 63);font-size: 16px;line-height: 1.8em;letter-spacing: 0.02em;text-align: left;font-weight: normal;\"><span leaf=\"\">展现了软件系统的整体形态</span></section></li><li><section style=\"margin-top: 5px;margin-bottom: 5px;color: rgb(63, 63, 63);font-size: 16px;line-height: 1.8em;letter-spacing: 0.02em;text-align: left;font-weight: normal;\"><span leaf=\"\">体现了高层次的技术决策</span></section></li><li><section style=\"margin-top: 5px;margin-bottom: 5px;color: rgb(63, 63, 63);font-size: 16px;line-height: 1.8em;letter-spacing: 0.02em;text-align: left;font-weight: normal;\"><span leaf=\"\">系统中的职责是如何分布的，容器间的是如何交互的</span></section></li><li><section style=\"margin-top: 5px;margin-bottom: 5px;color: rgb(63, 63, 63);font-size: 16px;line-height: 1.8em;letter-spacing: 0.02em;text-align: left;font-weight: normal;\"><span leaf=\"\">告诉开发者在哪里写代码</span></section></li></ul><p data-tool=\"mdnice编辑器\" style=\"color: rgb(63, 63, 63);font-size: 16px;line-height: 1.8em;letter-spacing: 0.02em;text-align: left;text-indent: 0em;margin-top: 0px;margin-bottom: 0px;margin-left: 0px;margin-right: 0px;padding-top: 16px;padding-bottom: 8px;padding-left: 0px;padding-right: 0px;\"><em style=\"color: rgb(0, 0, 0);font-style: italic;background-attachment: scroll;background-clip: border-box;background-color: rgba(0, 0, 0, 0);background-image: none;background-origin: padding-box;background-position-x: 0%;background-position-y: 0%;background-repeat: no-repeat;background-size: auto;width: auto;height: auto;margin-top: 0px;margin-bottom: 0px;margin-left: 0px;margin-right: 0px;padding-top: 0px;padding-bottom: 0px;padding-left: 0px;padding-right: 0px;border-top-style: none;border-bottom-style: none;border-left-style: none;border-right-style: none;border-top-width: 3px;border-bottom-width: 3px;border-left-width: 3px;border-right-width: 3px;border-top-color: rgba(0, 0, 0, 0.4);border-bottom-color: rgba(0, 0, 0, 0.4);border-left-color: rgba(0, 0, 0, 0.4);border-right-color: rgba(0, 0, 0, 0.4);border-top-left-radius: 0px;border-top-right-radius: 0px;border-bottom-right-radius: 0px;border-bottom-left-radius: 0px;\"><span leaf=\"\">怎么画</span></em></p><p data-tool=\"mdnice编辑器\" style=\"color: rgb(63, 63, 63);font-size: 16px;line-height: 1.8em;letter-spacing: 0.02em;text-align: left;text-indent: 0em;margin-top: 0px;margin-bottom: 0px;margin-left: 0px;margin-right: 0px;padding-top: 16px;padding-bottom: 8px;padding-left: 0px;padding-right: 0px;\"><span leaf=\"\">用一个框图来表示，内部可能包括名称、技术选择、职责，以及这些框图之间的交互，如果涉及外部系统，最好明确边界。</span></p><h3 data-tool=\"mdnice编辑器\" style=\" margin-top: 30px;margin-bottom: 15px;margin-left: 0px;margin-right: 0px;padding-top: 0px;padding-bottom: 0px;padding-left: 0px;padding-right: 0px;align-items: unset;background-attachment: scroll;background-clip: border-box;background-color: transparent;background-image: none;background-origin: padding-box;background-position-x: 0%;background-position-y: 0%;background-repeat: no-repeat;background-size: auto;border-top-style: none;border-bottom-style: none;border-left-style: none;border-right-style: none;border-top-width: 1px;border-bottom-width: 1px;border-left-width: 1px;border-right-width: 1px;border-top-color: rgb(0, 0, 0);border-bottom-color: rgb(0, 0, 0);border-left-color: rgb(0, 0, 0);border-right-color: rgb(0, 0, 0);border-top-left-radius: 0px;border-top-right-radius: 0px;border-bottom-left-radius: 0px;border-bottom-right-radius: 0px;box-shadow: none;display: block;flex-direction: unset;float: unset;height: auto;justify-content: unset;line-height: 1.5em;overflow-x: unset;overflow-y: unset; text-align: left;text-shadow: none;transform: none;width: auto;-webkit-box-reflect: unset; \"><span data-cacheurl=\"\" data-remoteid=\"\" style=\"background-attachment: scroll;background-clip: border-box;background-color: transparent;background-origin: padding-box;background-position-x: 0%;background-position-y: 0%;background-repeat: no-repeat;background-size: 15px 15px;width: 15px;height: 15px;align-items: unset;border-top-style: none;border-bottom-style: none;border-left-style: none;border-right-style: none;border-top-width: 1px;border-bottom-width: 1px;border-left-width: 1px;border-right-width: 1px;border-top-color: rgb(0, 0, 0);border-bottom-color: rgb(0, 0, 0);border-left-color: rgb(0, 0, 0);border-right-color: rgb(0, 0, 0);border-top-left-radius: 0px;border-top-right-radius: 0px;border-bottom-left-radius: 0px;border-bottom-right-radius: 0px;box-shadow: none;color: rgb(0, 0, 0);display: inline-block;font-size: 22px;font-weight: bold;flex-direction: unset;float: unset;justify-content: unset;letter-spacing: 0px;line-height: 1.5em;margin-top: 0px;margin-bottom: -2px;margin-left: 0px;margin-right: 0px;overflow-x: unset;overflow-y: unset;padding-top: 0px;padding-bottom: 0px;padding-left: 0px;padding-right: 0px; text-align: left;text-indent: 0em;text-shadow: none;transform: none;-webkit-box-reflect: unset;background-image: url(&quot;https://mmbiz.qpic.cn/sz_mmbiz_png/knmrNHnmCLE2wTd9jX9u0GWe2XtE2gG0T83kl6QcwWG6uyzeibHmM8L1OBErtgTKOKFub6CcZrDE4RZCITBZTcQ/640?wx_fmt=png&amp;from=appmsg&quot;);\"></span><span style=\"display: none;\"></span><span style=\" font-size: 16px;color: rgb(72, 179, 120);line-height: 1.5em;letter-spacing: 0em;align-items: unset;background-attachment: scroll;background-clip: border-box;background-color: transparent;background-image: none;background-origin: padding-box;background-position-x: 0%;background-position-y: 0%;background-repeat: no-repeat;background-size: auto;border-top-style: none;border-bottom-style: none;border-left-style: none;border-right-style: none;border-top-width: 1px;border-bottom-width: 1px;border-left-width: 1px;border-right-width: 1px;border-top-color: rgb(0, 0, 0);border-bottom-color: rgb(0, 0, 0);border-left-color: rgb(0, 0, 0);border-right-color: rgb(0, 0, 0);border-top-left-radius: 0px;border-top-right-radius: 0px;border-bottom-left-radius: 0px;border-bottom-right-radius: 0px;box-shadow: none;display: inline-block;font-weight: bold;flex-direction: unset;float: unset;height: auto;justify-content: unset;margin-top: 0px;margin-bottom: 0px;margin-left: 8px;margin-right: 0px;overflow-x: unset;overflow-y: unset;padding-top: 0px;padding-bottom: 0px;padding-left: 0px;padding-right: 0px; text-align: left;text-indent: 0em;text-shadow: none;transform: none;width: auto;-webkit-box-reflect: unset; \"><span leaf=\"\">3、组件图(Component Diagram)</span></span><span style=\"display: none;\"></span></h3><figure data-tool=\"mdnice编辑器\" style=\"margin-top: 10px;margin-bottom: 10px;margin-left: 0px;margin-right: 0px;padding-top: 0px;padding-bottom: 0px;padding-left: 0px;padding-right: 0px;display: flex;flex-direction: column;justify-content: center;align-items: center;\"><span leaf=\"\"><img alt=\"图片\" class=\"rich_pages wxw-img\" data-ratio=\"0.964095744680851\" data-src=\"https://mmbiz.qpic.cn/mmbiz_jpg/6mychickmupWDcvUWtYjTibVbFbG6N0LPJLXFbJ68RfQrEMdZ39gibVJYxxmLVfXeyPndLddz9eA7KKJaHqiccnoUQ/640?wx_fmt=other&amp;wxfrom=5&amp;wx_lazy=1&amp;wx_co=1&amp;tp=wxpic\" data-w=\"752\" style=\"display: block;margin-top: 0px;margin-right: auto;margin-bottom: 0px;margin-left: auto;max-width: 100%;border-top-style: none;border-bottom-style: none;border-left-style: none;border-right-style: none;border-top-width: 3px;border-bottom-width: 3px;border-left-width: 3px;border-right-width: 3px;border-top-color: rgba(0, 0, 0, 0.4);border-bottom-color: rgba(0, 0, 0, 0.4);border-left-color: rgba(0, 0, 0, 0.4);border-right-color: rgba(0, 0, 0, 0.4);border-top-left-radius: 4px;border-top-right-radius: 4px;border-bottom-right-radius: 4px;border-bottom-left-radius: 4px;object-fit: fill;box-shadow: rgba(0, 0, 0, 0) 0px 0px 0px 0px;\"></span><figcaption style=\"color: rgb(136, 136, 136);font-size: 12px;line-height: 1.5em;letter-spacing: 0em;text-align: center;font-weight: normal;margin-top: 5px;margin-bottom: 0px;margin-left: 0px;margin-right: 0px;padding-top: 0px;padding-bottom: 0px;padding-left: 0px;padding-right: 0px;\"><span leaf=\"\">图片</span></figcaption></figure><p data-tool=\"mdnice编辑器\" style=\"color: rgb(63, 63, 63);font-size: 16px;line-height: 1.8em;letter-spacing: 0.02em;text-align: left;text-indent: 0em;margin-top: 0px;margin-bottom: 0px;margin-left: 0px;margin-right: 0px;padding-top: 16px;padding-bottom: 8px;padding-left: 0px;padding-right: 0px;\"><span leaf=\"\">组件图是把某个容器进行展开，描述其内部的模块。</span></p><p data-tool=\"mdnice编辑器\" style=\"color: rgb(63, 63, 63);font-size: 16px;line-height: 1.8em;letter-spacing: 0.02em;text-align: left;text-indent: 0em;margin-top: 0px;margin-bottom: 0px;margin-left: 0px;margin-right: 0px;padding-top: 16px;padding-bottom: 8px;padding-left: 0px;padding-right: 0px;\"><em style=\"color: rgb(0, 0, 0);font-style: italic;background-attachment: scroll;background-clip: border-box;background-color: rgba(0, 0, 0, 0);background-image: none;background-origin: padding-box;background-position-x: 0%;background-position-y: 0%;background-repeat: no-repeat;background-size: auto;width: auto;height: auto;margin-top: 0px;margin-bottom: 0px;margin-left: 0px;margin-right: 0px;padding-top: 0px;padding-bottom: 0px;padding-left: 0px;padding-right: 0px;border-top-style: none;border-bottom-style: none;border-left-style: none;border-right-style: none;border-top-width: 3px;border-bottom-width: 3px;border-left-width: 3px;border-right-width: 3px;border-top-color: rgba(0, 0, 0, 0.4);border-bottom-color: rgba(0, 0, 0, 0.4);border-left-color: rgba(0, 0, 0, 0.4);border-right-color: rgba(0, 0, 0, 0.4);border-top-left-radius: 0px;border-top-right-radius: 0px;border-bottom-right-radius: 0px;border-bottom-left-radius: 0px;\"><span leaf=\"\">用途</span></em></p><p data-tool=\"mdnice编辑器\" style=\"color: rgb(63, 63, 63);font-size: 16px;line-height: 1.8em;letter-spacing: 0.02em;text-align: left;text-indent: 0em;margin-top: 0px;margin-bottom: 0px;margin-left: 0px;margin-right: 0px;padding-top: 16px;padding-bottom: 8px;padding-left: 0px;padding-right: 0px;\"><span leaf=\"\">这个图主要是给内部开发人员看的，怎么去做代码的组织和构建。其用途有：</span></p><ul style=\"list-style-type: disc;margin-top: 8px;margin-bottom: 8px;margin-left: 0px;margin-right: 0px;padding-top: 0px;padding-bottom: 0px;padding-left: 25px;padding-right: 0px;color: rgb(0, 0, 0);\" class=\"list-paddingleft-1\"><li><section style=\"margin-top: 5px;margin-bottom: 5px;color: rgb(63, 63, 63);font-size: 16px;line-height: 1.8em;letter-spacing: 0.02em;text-align: left;font-weight: normal;\"><span leaf=\"\">描述了系统由哪些组件/服务组成</span></section></li><li><section style=\"margin-top: 5px;margin-bottom: 5px;color: rgb(63, 63, 63);font-size: 16px;line-height: 1.8em;letter-spacing: 0.02em;text-align: left;font-weight: normal;\"><span leaf=\"\">厘清了组件之间的关系和依赖</span></section></li><li><section style=\"margin-top: 5px;margin-bottom: 5px;color: rgb(63, 63, 63);font-size: 16px;line-height: 1.8em;letter-spacing: 0.02em;text-align: left;font-weight: normal;\"><span leaf=\"\">为软件开发如何分解交付提供了框架</span></section></li></ul><h3 data-tool=\"mdnice编辑器\" style=\" margin-top: 30px;margin-bottom: 15px;margin-left: 0px;margin-right: 0px;padding-top: 0px;padding-bottom: 0px;padding-left: 0px;padding-right: 0px;align-items: unset;background-attachment: scroll;background-clip: border-box;background-color: transparent;background-image: none;background-origin: padding-box;background-position-x: 0%;background-position-y: 0%;background-repeat: no-repeat;background-size: auto;border-top-style: none;border-bottom-style: none;border-left-style: none;border-right-style: none;border-top-width: 1px;border-bottom-width: 1px;border-left-width: 1px;border-right-width: 1px;border-top-color: rgb(0, 0, 0);border-bottom-color: rgb(0, 0, 0);border-left-color: rgb(0, 0, 0);border-right-color: rgb(0, 0, 0);border-top-left-radius: 0px;border-top-right-radius: 0px;border-bottom-left-radius: 0px;border-bottom-right-radius: 0px;box-shadow: none;display: block;flex-direction: unset;float: unset;height: auto;justify-content: unset;line-height: 1.5em;overflow-x: unset;overflow-y: unset; text-align: left;text-shadow: none;transform: none;width: auto;-webkit-box-reflect: unset; \"><span data-cacheurl=\"\" data-remoteid=\"\" style=\"background-attachment: scroll;background-clip: border-box;background-color: transparent;background-origin: padding-box;background-position-x: 0%;background-position-y: 0%;background-repeat: no-repeat;background-size: 15px 15px;width: 15px;height: 15px;align-items: unset;border-top-style: none;border-bottom-style: none;border-left-style: none;border-right-style: none;border-top-width: 1px;border-bottom-width: 1px;border-left-width: 1px;border-right-width: 1px;border-top-color: rgb(0, 0, 0);border-bottom-color: rgb(0, 0, 0);border-left-color: rgb(0, 0, 0);border-right-color: rgb(0, 0, 0);border-top-left-radius: 0px;border-top-right-radius: 0px;border-bottom-left-radius: 0px;border-bottom-right-radius: 0px;box-shadow: none;color: rgb(0, 0, 0);display: inline-block;font-size: 22px;font-weight: bold;flex-direction: unset;float: unset;justify-content: unset;letter-spacing: 0px;line-height: 1.5em;margin-top: 0px;margin-bottom: -2px;margin-left: 0px;margin-right: 0px;overflow-x: unset;overflow-y: unset;padding-top: 0px;padding-bottom: 0px;padding-left: 0px;padding-right: 0px; text-align: left;text-indent: 0em;text-shadow: none;transform: none;-webkit-box-reflect: unset;background-image: url(&quot;https://mmbiz.qpic.cn/sz_mmbiz_png/knmrNHnmCLE2wTd9jX9u0GWe2XtE2gG0T83kl6QcwWG6uyzeibHmM8L1OBErtgTKOKFub6CcZrDE4RZCITBZTcQ/640?wx_fmt=png&amp;from=appmsg&quot;);\"></span><span style=\"display: none;\"></span><span style=\" font-size: 16px;color: rgb(72, 179, 120);line-height: 1.5em;letter-spacing: 0em;align-items: unset;background-attachment: scroll;background-clip: border-box;background-color: transparent;background-image: none;background-origin: padding-box;background-position-x: 0%;background-position-y: 0%;background-repeat: no-repeat;background-size: auto;border-top-style: none;border-bottom-style: none;border-left-style: none;border-right-style: none;border-top-width: 1px;border-bottom-width: 1px;border-left-width: 1px;border-right-width: 1px;border-top-color: rgb(0, 0, 0);border-bottom-color: rgb(0, 0, 0);border-left-color: rgb(0, 0, 0);border-right-color: rgb(0, 0, 0);border-top-left-radius: 0px;border-top-right-radius: 0px;border-bottom-left-radius: 0px;border-bottom-right-radius: 0px;box-shadow: none;display: inline-block;font-weight: bold;flex-direction: unset;float: unset;height: auto;justify-content: unset;margin-top: 0px;margin-bottom: 0px;margin-left: 8px;margin-right: 0px;overflow-x: unset;overflow-y: unset;padding-top: 0px;padding-bottom: 0px;padding-left: 0px;padding-right: 0px; text-align: left;text-indent: 0em;text-shadow: none;transform: none;width: auto;-webkit-box-reflect: unset; \"><span leaf=\"\">4、类图(Code/Class Diagram)</span></span><span style=\"display: none;\"></span></h3><figure data-tool=\"mdnice编辑器\" style=\"margin-top: 10px;margin-bottom: 10px;margin-left: 0px;margin-right: 0px;padding-top: 0px;padding-bottom: 0px;padding-left: 0px;padding-right: 0px;display: flex;flex-direction: column;justify-content: center;align-items: center;\"><span leaf=\"\"><img data-src=\"https://mmbiz.qpic.cn/mmbiz_jpg/6mychickmupWDcvUWtYjTibVbFbG6N0LPJYItUibicOchTSYXFuHvfUZvFR7nyaM7v8gZRiaXr369KnS90SziavPt2VA/640?wx_fmt=other&amp;wxfrom=5&amp;wx_lazy=1&amp;wx_co=1&amp;tp=wxpic\" alt=\"图片\" class=\"rich_pages wxw-img\" data-ratio=\"0.7035647279549718\" data-w=\"1066\" style=\"display: block;margin-top: 0px;margin-right: auto;margin-bottom: 0px;margin-left: auto;max-width: 100%;border-top-style: none;border-bottom-style: none;border-left-style: none;border-right-style: none;border-top-width: 3px;border-bottom-width: 3px;border-left-width: 3px;border-right-width: 3px;border-top-color: rgba(0, 0, 0, 0.4);border-bottom-color: rgba(0, 0, 0, 0.4);border-left-color: rgba(0, 0, 0, 0.4);border-right-color: rgba(0, 0, 0, 0.4);border-top-left-radius: 4px;border-top-right-radius: 4px;border-bottom-right-radius: 4px;border-bottom-left-radius: 4px;object-fit: fill;box-shadow: rgba(0, 0, 0, 0) 0px 0px 0px 0px;\"></span><figcaption style=\"color: rgb(136, 136, 136);font-size: 12px;line-height: 1.5em;letter-spacing: 0em;text-align: center;font-weight: normal;margin-top: 5px;margin-bottom: 0px;margin-left: 0px;margin-right: 0px;padding-top: 0px;padding-bottom: 0px;padding-left: 0px;padding-right: 0px;\"><span leaf=\"\">图片</span></figcaption></figure><p data-tool=\"mdnice编辑器\" style=\"color: rgb(63, 63, 63);font-size: 16px;line-height: 1.8em;letter-spacing: 0.02em;text-align: left;text-indent: 0em;margin-top: 0px;margin-bottom: 0px;margin-left: 0px;margin-right: 0px;padding-top: 16px;padding-bottom: 8px;padding-left: 0px;padding-right: 0px;\"><span leaf=\"\">这个图很显然是给技术人员看的，比较常见，就不详细介绍了。</span></p><h2 data-cacheurl=\"\" data-remoteid=\"\" data-tool=\"mdnice编辑器\" style=\"margin-top: 30px;margin-bottom: 15px;margin-left: 0px;margin-right: 0px;padding-left: 0px;padding-right: 0px;background-attachment: scroll;background-clip: border-box;background-color: transparent;background-origin: padding-box;background-position-x: 50%;background-position-y: 50%;background-repeat: no-repeat;background-size: 63px;width: auto;height: auto;align-items: unset;border-top-style: none;border-bottom-style: none;border-left-style: none;border-right-style: none;border-top-width: 1px;border-bottom-width: 1px;border-left-width: 1px;border-right-width: 1px;border-top-color: rgb(0, 0, 0);border-bottom-color: rgb(0, 0, 0);border-left-color: rgb(0, 0, 0);border-right-color: rgb(0, 0, 0);border-top-left-radius: 0px;border-top-right-radius: 0px;border-bottom-left-radius: 0px;border-bottom-right-radius: 0px;box-shadow: none;display: flex;flex-direction: unset;float: unset;justify-content: center;line-height: 1.5em;overflow-x: unset;overflow-y: unset;padding-top: 0px;padding-bottom: 0px; text-align: left;text-shadow: none;transform: none;-webkit-box-reflect: unset;background-image: url(&quot;https://mmbiz.qpic.cn/sz_mmbiz_png/knmrNHnmCLE2wTd9jX9u0GWe2XtE2gG0Bs9yY5iagVWlfZlVX6Pj0NCx7WsN5oabG7Tc4J2CibjIWuTgUfeVcVVQ/640?wx_fmt=png&amp;from=appmsg&quot;);\"><span style=\"display: none;\"></span><span style=\" font-size: 18px;color: rgb(72, 179, 120);line-height: 2.4em;letter-spacing: 0em;margin-top: 38px;margin-bottom: 10px;border-top-style: none;border-bottom-style: none;border-left-style: none;border-right-style: none;border-top-width: 1px;border-bottom-width: 1px;border-left-width: 1px;border-right-width: 1px;border-top-color: rgb(0, 0, 0);border-bottom-color: rgb(0, 0, 0);border-left-color: rgb(0, 0, 0);border-right-color: rgb(0, 0, 0);border-top-left-radius: 0px;border-top-right-radius: 0px;border-bottom-left-radius: 0px;border-bottom-right-radius: 0px;align-items: unset;background-attachment: scroll;background-clip: border-box;background-color: transparent;background-image: none;background-origin: padding-box;background-position-x: 0%;background-position-y: 0%;background-repeat: no-repeat;background-size: auto;box-shadow: none;display: inline-block;font-weight: bold;flex-direction: unset;float: unset;height: 38px;justify-content: unset;margin-left: 0px;margin-right: 0px;overflow-x: unset;overflow-y: unset;padding-top: 0px;padding-bottom: 0px;padding-left: 0px;padding-right: 0px; text-align: center;text-indent: 0em;text-shadow: none;transform: none;width: auto;-webkit-box-reflect: unset; \"><strong style=\" align-items: unset;background-attachment: scroll;background-clip: border-box;background-color: transparent;background-image: none;background-origin: padding-box;background-position-x: 0%;background-position-y: 0%;background-repeat: no-repeat;background-size: auto;border-top-style: none;border-bottom-style: none;border-left-style: none;border-right-style: none;border-top-width: 1px;border-bottom-width: 1px;border-left-width: 1px;border-right-width: 1px;border-top-color: rgb(0, 0, 0);border-bottom-color: rgb(0, 0, 0);border-left-color: rgb(0, 0, 0);border-right-color: rgb(0, 0, 0);border-top-left-radius: 0px;border-top-right-radius: 0px;border-bottom-left-radius: 0px;border-bottom-right-radius: 0px;box-shadow: none;color: rgb(0, 0, 0);display: inline-block;font-size: 22px;font-weight: bold;flex-direction: unset;float: unset;height: auto;justify-content: unset;letter-spacing: 0px;line-height: 1.5em;margin-top: 0px;margin-bottom: 0px;margin-left: 0px;margin-right: 0px;overflow-x: unset;overflow-y: unset;padding-top: 0px;padding-bottom: 0px;padding-left: 0px;padding-right: 0px; text-align: left;text-indent: 0em;text-shadow: none;transform: none;width: auto;-webkit-box-reflect: unset; \"><span leaf=\"\">案例分享</span></strong></span><span style=\"display: none;\"></span></h2><p data-tool=\"mdnice编辑器\" style=\"color: rgb(63, 63, 63);font-size: 16px;line-height: 1.8em;letter-spacing: 0.02em;text-align: left;text-indent: 0em;margin-top: 0px;margin-bottom: 0px;margin-left: 0px;margin-right: 0px;padding-top: 16px;padding-bottom: 8px;padding-left: 0px;padding-right: 0px;\"><span leaf=\"\">下面是内部的一个实时数据工具的架构图。作为一个应该自描述的架构图，这里不多做解释了。如果有看不明白的，那肯定是还画的不够好。</span></p><figure data-tool=\"mdnice编辑器\" style=\"margin-top: 10px;margin-bottom: 10px;margin-left: 0px;margin-right: 0px;padding-top: 0px;padding-bottom: 0px;padding-left: 0px;padding-right: 0px;display: flex;flex-direction: column;justify-content: center;align-items: center;\"><span leaf=\"\"><img alt=\"图片\" class=\"rich_pages wxw-img\" data-ratio=\"0.7287037037037037\" data-src=\"https://mmbiz.qpic.cn/mmbiz_jpg/6mychickmupWDcvUWtYjTibVbFbG6N0LPJn8Pjz6pCByEeemR0X99aibsd3Lic4EDsWalmAia62H2c1KhAKDfaNE4Zg/640?wx_fmt=other&amp;wxfrom=5&amp;wx_lazy=1&amp;wx_co=1&amp;tp=wxpic\" data-w=\"1080\" style=\"display: block;margin-top: 0px;margin-right: auto;margin-bottom: 0px;margin-left: auto;max-width: 100%;border-top-style: none;border-bottom-style: none;border-left-style: none;border-right-style: none;border-top-width: 3px;border-bottom-width: 3px;border-left-width: 3px;border-right-width: 3px;border-top-color: rgba(0, 0, 0, 0.4);border-bottom-color: rgba(0, 0, 0, 0.4);border-left-color: rgba(0, 0, 0, 0.4);border-right-color: rgba(0, 0, 0, 0.4);border-top-left-radius: 4px;border-top-right-radius: 4px;border-bottom-right-radius: 4px;border-bottom-left-radius: 4px;object-fit: fill;box-shadow: rgba(0, 0, 0, 0) 0px 0px 0px 0px;\"></span><figcaption style=\"color: rgb(136, 136, 136);font-size: 12px;line-height: 1.5em;letter-spacing: 0em;text-align: center;font-weight: normal;margin-top: 5px;margin-bottom: 0px;margin-left: 0px;margin-right: 0px;padding-top: 0px;padding-bottom: 0px;padding-left: 0px;padding-right: 0px;\"><span leaf=\"\">图片</span></figcaption></figure><p data-tool=\"mdnice编辑器\" style=\"color: rgb(63, 63, 63);font-size: 16px;line-height: 1.8em;letter-spacing: 0.02em;text-align: left;text-indent: 0em;margin-top: 0px;margin-bottom: 0px;margin-left: 0px;margin-right: 0px;padding-top: 16px;padding-bottom: 8px;padding-left: 0px;padding-right: 0px;\"><span leaf=\"\">画好架构图可能有许多方法论，本篇主要介绍了C4这种方法，C4的理论也是不断进化的。但不论是哪种画图方法论，我们回到画图初衷，更好的交流，我们在画的过程中不必被条条框框所限制。简而言之，画之前想好：画图给谁看，看什么，怎么样不解释就看懂。</span></p><h2 data-cacheurl=\"\" data-remoteid=\"\" data-tool=\"mdnice编辑器\" style=\"margin-top: 30px;margin-bottom: 15px;margin-left: 0px;margin-right: 0px;padding-left: 0px;padding-right: 0px;background-attachment: scroll;background-clip: border-box;background-color: transparent;background-origin: padding-box;background-position-x: 50%;background-position-y: 50%;background-repeat: no-repeat;background-size: 63px;width: auto;height: auto;align-items: unset;border-top-style: none;border-bottom-style: none;border-left-style: none;border-right-style: none;border-top-width: 1px;border-bottom-width: 1px;border-left-width: 1px;border-right-width: 1px;border-top-color: rgb(0, 0, 0);border-bottom-color: rgb(0, 0, 0);border-left-color: rgb(0, 0, 0);border-right-color: rgb(0, 0, 0);border-top-left-radius: 0px;border-top-right-radius: 0px;border-bottom-left-radius: 0px;border-bottom-right-radius: 0px;box-shadow: none;display: flex;flex-direction: unset;float: unset;justify-content: center;line-height: 1.5em;overflow-x: unset;overflow-y: unset;padding-top: 0px;padding-bottom: 0px; text-align: left;text-shadow: none;transform: none;-webkit-box-reflect: unset;background-image: url(&quot;https://mmbiz.qpic.cn/sz_mmbiz_png/knmrNHnmCLE2wTd9jX9u0GWe2XtE2gG0Bs9yY5iagVWlfZlVX6Pj0NCx7WsN5oabG7Tc4J2CibjIWuTgUfeVcVVQ/640?wx_fmt=png&amp;from=appmsg&quot;);\"><span style=\"display: none;\"></span><span style=\" font-size: 18px;color: rgb(72, 179, 120);line-height: 2.4em;letter-spacing: 0em;margin-top: 38px;margin-bottom: 10px;border-top-style: none;border-bottom-style: none;border-left-style: none;border-right-style: none;border-top-width: 1px;border-bottom-width: 1px;border-left-width: 1px;border-right-width: 1px;border-top-color: rgb(0, 0, 0);border-bottom-color: rgb(0, 0, 0);border-left-color: rgb(0, 0, 0);border-right-color: rgb(0, 0, 0);border-top-left-radius: 0px;border-top-right-radius: 0px;border-bottom-left-radius: 0px;border-bottom-right-radius: 0px;align-items: unset;background-attachment: scroll;background-clip: border-box;background-color: transparent;background-image: none;background-origin: padding-box;background-position-x: 0%;background-position-y: 0%;background-repeat: no-repeat;background-size: auto;box-shadow: none;display: inline-block;font-weight: bold;flex-direction: unset;float: unset;height: 38px;justify-content: unset;margin-left: 0px;margin-right: 0px;overflow-x: unset;overflow-y: unset;padding-top: 0px;padding-bottom: 0px;padding-left: 0px;padding-right: 0px; text-align: center;text-indent: 0em;text-shadow: none;transform: none;width: auto;-webkit-box-reflect: unset; \"><strong style=\" align-items: unset;background-attachment: scroll;background-clip: border-box;background-color: transparent;background-image: none;background-origin: padding-box;background-position-x: 0%;background-position-y: 0%;background-repeat: no-repeat;background-size: auto;border-top-style: none;border-bottom-style: none;border-left-style: none;border-right-style: none;border-top-width: 1px;border-bottom-width: 1px;border-left-width: 1px;border-right-width: 1px;border-top-color: rgb(0, 0, 0);border-bottom-color: rgb(0, 0, 0);border-left-color: rgb(0, 0, 0);border-right-color: rgb(0, 0, 0);border-top-left-radius: 0px;border-top-right-radius: 0px;border-bottom-left-radius: 0px;border-bottom-right-radius: 0px;box-shadow: none;color: rgb(0, 0, 0);display: inline-block;font-size: 22px;font-weight: bold;flex-direction: unset;float: unset;height: auto;justify-content: unset;letter-spacing: 0px;line-height: 1.5em;margin-top: 0px;margin-bottom: 0px;margin-left: 0px;margin-right: 0px;overflow-x: unset;overflow-y: unset;padding-top: 0px;padding-bottom: 0px;padding-left: 0px;padding-right: 0px; text-align: left;text-indent: 0em;text-shadow: none;transform: none;width: auto;-webkit-box-reflect: unset; \"><span leaf=\"\">参考资料：</span></strong></span><span style=\"display: none;\"></span></h2><p data-tool=\"mdnice编辑器\" style=\"color: rgb(63, 63, 63);font-size: 16px;line-height: 1em;letter-spacing: 0.02em;text-align: left;text-indent: 0em;margin: 0px;padding: 16px 0px 8px;\"><span leaf=\"\" style=\"font-size: 14px;\" mpa-font-style=\"m8o3oso1mbt\"><span textstyle=\"\" style=\"color: rgb(136, 136, 136);\">C4官网：</span></span></p><p data-tool=\"mdnice编辑器\" style=\"color: rgb(63, 63, 63);font-size: 16px;line-height: 1em;letter-spacing: 0.02em;text-align: left;text-indent: 0em;margin: 0px;padding: 16px 0px 8px;\"><span leaf=\"\" style=\"font-size: 14px;\" mpa-font-style=\"m8o3oso117xw\"><span textstyle=\"\" style=\"color: rgb(136, 136, 136);\">https://c4model.com/</span></span></p><p data-tool=\"mdnice编辑器\" style=\"color: rgb(63, 63, 63);font-size: 16px;line-height: 1em;letter-spacing: 0.02em;text-align: left;text-indent: 0em;margin: 0px;padding: 16px 0px 8px;\"><span leaf=\"\" style=\"font-size: 14px;\" mpa-font-style=\"m8o3oso11wmx\"><span textstyle=\"\" style=\"color: rgb(136, 136, 136);\">为什么需要软件架构图：</span></span></p><p data-tool=\"mdnice编辑器\" style=\"color: rgb(63, 63, 63);font-size: 16px;line-height: 1em;letter-spacing: 0.02em;text-align: left;text-indent: 0em;margin: 0px;padding: 16px 0px 8px;\"><span leaf=\"\" style=\"font-size: 14px;\" mpa-font-style=\"m8o3oso115xr\"><span textstyle=\"\" style=\"color: rgb(136, 136, 136);\">https://www.infoq.cn/article/GhprrUlOYyOqS8*FR1pH</span></span></p><p data-tool=\"mdnice编辑器\" style=\"color: rgb(63, 63, 63);font-size: 16px;line-height: 1em;letter-spacing: 0.02em;text-align: left;text-indent: 0em;margin: 0px;padding: 16px 0px 8px;\"><span leaf=\"\" style=\"font-size: 14px;\" mpa-font-style=\"m8o3oso112mn\"><span textstyle=\"\" style=\"color: rgb(136, 136, 136);\">书籍：《程序员必读之软件架构》</span></span></p></section><section data-tool=\"mdnice编辑器\" class=\"js_darkmode__1\" data-pm-slice=\"0 0 []\" style=\"-webkit-tap-highlight-color: transparent;margin: 20px 0px;padding: 10px 10px 10px 20px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;font-style: normal;font-variant-ligatures: normal;font-variant-caps: normal;font-weight: 400;orphans: 2;text-indent: 0px;text-transform: none;widows: 2;word-spacing: 0px;-webkit-text-stroke-width: 0px;white-space: normal;text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;font-family: Optima-Regular, Optima, PingFangSC-light, PingFangTC-light, &quot;PingFang SC&quot;, Cambria, Cochin, Georgia, Times, &quot;Times New Roman&quot;, serif;letter-spacing: normal;text-align: left;border-width: 3px;border-style: none none none solid;color: var(--weui-FG-1);font-size: 15px;border-color: rgba(0, 0, 0, 0.4) rgba(0, 0, 0, 0.4) rgba(0, 0, 0, 0.4) rgb(53, 179, 120);background: none 0% 0% / auto no-repeat scroll padding-box border-box rgb(251, 249, 253);border-radius: 0px;width: auto;height: auto;box-shadow: rgba(0, 0, 0, 0) 0px 0px 0px 0px;overflow: auto;visibility: visible;\"><p class=\"js_darkmode__2\" style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 16px 0px 8px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;clear: both;min-height: 1em;color: rgb(63, 63, 63);text-indent: 0em;font-size: 16px;line-height: 1.8em;letter-spacing: 0.02em;visibility: visible;\"><span leaf=\"\" style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;visibility: visible;\">👉 欢迎</span><a href=\"https://mp.weixin.qq.com/s?__biz=MzU4MDUyMDQyNQ==&amp;mid=2247538511&amp;idx=1&amp;sn=c2f80c1f6143af7bfcaa661951d7bf23&amp;chksm=fd5787c9ca200edf9b861f51dcdc6e15a575488ab6be0f5b721ea27047cf3316e07ea33a9a8d&amp;token=343952052&amp;lang=zh_CN&amp;scene=21#wechat_redirect\" data-linktype=\"2\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);margin: 0px;padding: 0px;outline: 0px;color: rgb(72, 179, 120);text-decoration: none;-webkit-user-drag: none;cursor: pointer;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;letter-spacing: 0.02em;text-indent: 0em;word-spacing: 0em;caret-color: transparent;font-weight: bold;border-style: none none solid;border-width: 1px;border-color: rgb(30, 107, 184) rgb(30, 107, 184) rgb(72, 179, 120);border-radius: 0px;visibility: visible;\"><span leaf=\"\" style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;visibility: visible;\">加入小哈的星球</span></a><span style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;letter-spacing: 0.02em;text-indent: 0em;word-spacing: 0em;caret-color: transparent;visibility: visible;\"><span leaf=\"\" style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;visibility: visible;\">，你将获得:&nbsp;</span></span><strong style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;text-indent: 0em;caret-color: transparent;color: rgb(255, 104, 39);font-size: 15px;letter-spacing: 0.51px;word-spacing: 0.8px;visibility: visible;\"><span leaf=\"\" style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;visibility: visible;\">专属的项目实战 / 1v1 提问 /&nbsp;</span><strong style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;letter-spacing: 0.51px;word-spacing: 0.8px;visibility: visible;\"><span leaf=\"\" style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;visibility: visible;\">Java 学习路线 /&nbsp;</span></strong><span leaf=\"\" style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;visibility: visible;\">学习打卡 / 每月赠书 / 社群讨论</span></strong></p><ul style=\"-webkit-tap-highlight-color: transparent;margin: 8px 0px;padding: 0px 0px 0px 25px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;width: 524.469px;color: rgb(0, 0, 0);visibility: visible;\" class=\"list-paddingleft-1\"><li style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;visibility: visible;\"><section class=\"js_darkmode__4\" style=\"-webkit-tap-highlight-color: transparent;margin: 5px 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;color: rgb(63, 63, 63);font-size: 16px;line-height: 1.8em;letter-spacing: 0.02em;visibility: visible;\"><span style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;font-size: 15px;visibility: visible;\"><span leaf=\"\" style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;visibility: visible;\">新项目:</span><strong class=\"js_darkmode__5\" style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;border-color: rgba(0, 0, 0, 0.4);color: rgb(74, 74, 74);background: none 0% 0% / auto no-repeat scroll padding-box border-box rgba(0, 0, 0, 0);width: auto;height: auto;border-style: none;border-width: 3px;border-radius: 0px;visibility: visible;\"><span leaf=\"\" style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;visibility: visible;\">《从零手撸：仿小红书（微服务架构）》</span></strong><span leaf=\"\" style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;visibility: visible;\">&nbsp;正在持续爆肝中，基于 Spring Cloud Alibaba + Spring Boot 3.x + JDK 17...,&nbsp;</span></span><a href=\"https://mp.weixin.qq.com/s?__biz=MzU4MDUyMDQyNQ==&amp;mid=2247538491&amp;idx=1&amp;sn=576995017721766d0fe15723fd135619&amp;chksm=fd5787bdca200eab54d2fb8ca07fcc2bffdec3eaab4ab82ab5eaf949f0254c1683455e02010b&amp;token=343952052&amp;lang=zh_CN&amp;scene=21#wechat_redirect\" data-linktype=\"2\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);margin: 0px;padding: 0px;outline: 0px;color: rgb(72, 179, 120);text-decoration: none;-webkit-user-drag: none;cursor: pointer;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;letter-spacing: 0.02em;text-indent: 0em;word-spacing: 0em;font-weight: bold;border-style: none none solid;border-width: 1px;border-color: rgb(30, 107, 184) rgb(30, 107, 184) rgb(72, 179, 120);border-radius: 0px;visibility: visible;font-size: 15px;\"><span style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;visibility: visible;\"><span leaf=\"\" style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;visibility: visible;\">点击查看项目介绍</span></span></a><span style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;font-size: 15px;visibility: visible;\"><span leaf=\"\" style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;visibility: visible;\">；</span></span></section></li><li style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;visibility: visible;\"><section class=\"js_darkmode__6\" style=\"-webkit-tap-highlight-color: transparent;margin: 5px 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;color: rgb(63, 63, 63);font-size: 16px;line-height: 1.8em;letter-spacing: 0.02em;visibility: visible;\"><span style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;font-size: 15px;visibility: visible;\"><strong class=\"js_darkmode__7\" style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;border-color: rgba(0, 0, 0, 0.4);color: rgb(74, 74, 74);background: none 0% 0% / auto no-repeat scroll padding-box border-box rgba(0, 0, 0, 0);width: auto;height: auto;border-style: none;border-width: 3px;border-radius: 0px;visibility: visible;\"><span leaf=\"\" style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;visibility: visible;\">《从零手撸：前后端分离博客项目（全栈开发）》</span></strong><span leaf=\"\" style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;visibility: visible;\">&nbsp;2期已完结,演示链接：</span><span class=\"js_darkmode__8\" style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;color: rgb(0, 122, 170);letter-spacing: 0.544px;word-spacing: 0.8px;text-decoration: underline;visibility: visible;\"><span leaf=\"\" style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;visibility: visible;\">http://116.62.199.48/</span></span><span leaf=\"\" style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;visibility: visible;\">;</span></span></section></li><li style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;visibility: visible;\"><section class=\"js_darkmode__6\" style=\"-webkit-tap-highlight-color: transparent;margin: 5px 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;color: rgb(63, 63, 63);font-size: 16px;line-height: 1.8em;letter-spacing: 0.02em;visibility: visible;\"><span style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;font-size: 15px;visibility: visible;\"><span leaf=\"\" style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;visibility: visible;\">专栏阅读地址：</span><span style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;text-decoration: underline;color: rgb(0, 122, 170);visibility: visible;\"><span leaf=\"\" style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;visibility: visible;\">https://www.quanxiaoha.com/column</span></span></span><span leaf=\"\" style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;visibility: visible;\"><br></span></section></li></ul><p class=\"js_darkmode__9\" style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 16px 0px 8px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;clear: both;min-height: 1em;color: rgb(63, 63, 63);text-indent: 0em;font-size: 16px;line-height: 1.8em;letter-spacing: 0.02em;visibility: visible;\"><span style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;font-size: 15px;visibility: visible;\"><span leaf=\"\" style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;visibility: visible;\">截止目前，</span><strong style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;visibility: visible;\"><span leaf=\"\" style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;visibility: visible;\">累计输出 85w+ 字，讲解图 3088+ 张，还在持续爆肝中..</span></strong><span leaf=\"\" style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;visibility: visible;\">&nbsp;后续还会上新更多项目，目标是将 Java 领域典型的项目都整一波，如秒杀系统, 在线商城, IM 即时通讯，Spring Cloud Alibaba 等等，</span></span><a href=\"https://mp.weixin.qq.com/s?__biz=MzU4MDUyMDQyNQ==&amp;mid=2247538511&amp;idx=1&amp;sn=c2f80c1f6143af7bfcaa661951d7bf23&amp;chksm=fd5787c9ca200edf9b861f51dcdc6e15a575488ab6be0f5b721ea27047cf3316e07ea33a9a8d&amp;token=343952052&amp;lang=zh_CN&amp;scene=21#wechat_redirect\" data-linktype=\"2\" style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);margin: 0px;padding: 0px;outline: 0px;color: rgb(72, 179, 120);text-decoration: none;-webkit-user-drag: none;cursor: pointer;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;letter-spacing: 0.02em;text-indent: 0em;word-spacing: 0em;font-weight: bold;border-style: none none solid;border-width: 1px;border-color: rgb(30, 107, 184) rgb(30, 107, 184) rgb(72, 179, 120);border-radius: 0px;visibility: visible;font-size: 15px;\"><span style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;visibility: visible;\"><span leaf=\"\" style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;visibility: visible;\">戳我加入学习，解锁全部项目，已有3000+小伙伴加入</span></span></a></p><span leaf=\"\" style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;visibility: visible;\"><img alt=\"图片\" class=\"rich_pages wxw-img __bg_gif\" data-imgfileid=\"100073709\" data-ratio=\"0.4782205746061168\" data-src=\"https://mmbiz.qpic.cn/sz_mmbiz_gif/knmrNHnmCLFJRHck1SBQN2jMyV6c8uyy9cMShUicdfia3ibdNhX9qu5NpRlCYicDIoVMHMddwNuzI6NUSKnkHfL8Lg/640?wx_fmt=gif&amp;from=appmsg&amp;wxfrom=5&amp;wx_lazy=1&amp;wx_co=1&amp;tp=webp\" data-type=\"gif\" data-w=\"1079\" style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;vertical-align: bottom;height: auto !important;color: rgb(106, 115, 125);font-size: 14.4px;letter-spacing: 0.544px;text-align: center;word-spacing: 0.8px;border-radius: 8px;background-size: 16px !important;visibility: visible !important;width: 594px !important;\"></span></section><section data-tool=\"mdnice编辑器\" data-website=\"https://www.mdnice.com\" nodeleaf=\"\" data-pm-slice=\"0 0 []\" style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px 10px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;font-style: normal;font-variant-ligatures: normal;font-variant-caps: normal;font-weight: 400;orphans: 2;text-indent: 0px;text-transform: none;widows: 2;word-spacing: 0em;-webkit-text-stroke-width: 0px;white-space: normal;text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;color: rgb(0, 0, 0);font-family: Optima-Regular, Optima, PingFangSC-light, PingFangTC-light, &quot;PingFang SC&quot;, Cambria, Cochin, Georgia, Times, &quot;Times New Roman&quot;, serif;font-size: 16px;letter-spacing: 0em;background: none 0% 0% / auto no-repeat scroll padding-box border-box rgb(255, 255, 255);width: auto;line-height: 1.5em;word-break: break-word;text-align: center;\"><img alt=\"图片\" class=\"rich_pages wxw-img\" data-croporisrc=\"https://mmbiz.qpic.cn/sz_mmbiz_png/knmrNHnmCLEThiaTYH7Qds3n3l927cIQdDRqLyaEibbcDpFrMwZW3HqshYuJZwfT6iaJY5FptoEk4SXtibYeRdSVRA/0?wx_fmt=png&amp;from=appmsg\" data-cropx1=\"0\" data-cropx2=\"640\" data-cropy1=\"218.46153846153845\" data-cropy2=\"778.4615384615383\" data-imgfileid=\"100073711\" data-ratio=\"0.875\" data-s=\"300,640\" data-src=\"https://mmbiz.qpic.cn/sz_mmbiz_jpg/knmrNHnmCLEoSFjJsicQibhicoKH5HhIvkfGqyK8gicUic2sQTFicvJLt3icKk3yduqvFyqa0zWrBQibO7Cg87iaMJnpDQg/640?wx_fmt=other&amp;wxfrom=5&amp;wx_lazy=1&amp;wx_co=1&amp;tp=webp\" data-type=\"jpeg\" data-w=\"640\" style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;vertical-align: bottom;height: auto !important;color: rgb(63, 63, 63);font-family: Optima-Regular, Optima, PingFangSC-light, PingFangTC-light, &quot;PingFang SC&quot;, Cambria, Cochin, Georgia, Times, &quot;Times New Roman&quot;, serif;font-size: 16px;font-weight: 700;letter-spacing: 0.034em;caret-color: rgb(0, 0, 0);background-color: rgb(255, 255, 255);border-radius: 8px;text-align: center;background-size: 16px !important;visibility: visible !important;width: 239px !important;\"></section><section data-tool=\"mdnice编辑器\" data-website=\"https://www.mdnice.com\" style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px 10px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;font-style: normal;font-variant-ligatures: normal;font-variant-caps: normal;font-weight: 400;orphans: 2;text-indent: 0px;text-transform: none;widows: 2;word-spacing: 0em;-webkit-text-stroke-width: 0px;white-space: normal;text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;color: rgb(0, 0, 0);font-size: 16px;letter-spacing: 0em;text-align: left;font-family: Optima, &quot;Microsoft YaHei&quot;, PingFangSC-regular, serif;background: none 0% 0% / auto no-repeat scroll padding-box border-box rgb(255, 255, 255);width: auto;height: auto;line-height: 1.5em;word-break: break-word;\"><pre style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;letter-spacing: 0.544px;font-variant-ligatures: common-ligatures;font-weight: 700;orphans: 4;widows: 1;word-spacing: 1px;caret-color: rgb(255, 0, 0);\"><section data-tool=\"mdnice编辑器\" data-website=\"https://www.mdnice.com\" nodeleaf=\"\" style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px 10px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;font-family: Optima-Regular, Optima, PingFangSC-light, PingFangTC-light, &quot;PingFang SC&quot;, Cambria, Cochin, Georgia, Times, &quot;Times New Roman&quot;, serif;white-space: normal;font-size: 15px;line-height: 1.6;word-break: break-word;letter-spacing: 0.05em;color: rgb(89, 89, 89);text-align: center;\"><img data-src=\"https://mmbiz.qpic.cn/mmbiz_gif/TNUwKhV0JpTGQqtlGfEHkjibtshlaDwVKzjqq2pnpmYC14bKxDtSuhpWZWfVcicj5PFsoSMzuzicKIWZbsBpGXiaicg/640?wx_fmt=gif&amp;wxfrom=5&amp;wx_lazy=1&amp;tp=webp\" alt=\"图片\" class=\"__bg_gif rich_pages wxw-img\" data-ratio=\"0.08658008658008658\" data-type=\"gif\" data-w=\"462\" style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;vertical-align: bottom;height: auto !important;background-size: 16px;border-radius: 8px;font-family: -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif;letter-spacing: 0.544px;word-spacing: 2px;display: block;visibility: visible !important;width: 577.977px !important;\" data-width=\"100%\" data-fileid=\"100015743\" data-imgfileid=\"100050504\"></section><pre style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;font-size: 15px;color: rgb(89, 89, 89);font-family: -apple-system, system-ui, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif;white-space: normal;\"><pre style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;color: rgb(0, 0, 0);font-size: 16px;\"><pre data-style=\"letter-spacing: 0.544px; font-size: 16px; color: rgb(63, 63, 63); word-spacing: 1px; line-height: inherit;\" style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;color: rgb(63, 63, 63);line-height: inherit;\"><section data-mpa-template-id=\"1250\" data-mpa-color=\"#ffffff\" data-mpa-category=\"divider\" data-darkmode-bgcolor=\"rgb(36, 36, 36)\" data-darkmode-original-bgcolor=\"rgb(255, 255, 255)\" data-darkmode-color=\"rgb(230, 230, 230)\" data-darkmode-original-color=\"rgb(0, 0, 0)\" data-style=\"margin-right: 0.5em; margin-left: 0.5em; white-space: normal; font-family: -apple-system-font, system-ui, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; color: rgb(0, 0, 0); letter-spacing: 0px; word-spacing: 2px;\" style=\"-webkit-tap-highlight-color: transparent;margin: 0px 0.5em;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;white-space: normal;font-family: -apple-system-font, system-ui, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif;color: rgb(0, 0, 0);letter-spacing: 0px;word-spacing: 2px;\"><section powered-by=\"xiumi.us\" data-darkmode-bgcolor=\"rgb(36, 36, 36)\" data-darkmode-original-bgcolor=\"rgb(255, 255, 255)\" data-darkmode-color=\"rgb(138, 138, 138)\" data-darkmode-original-color=\"rgb(89, 89, 89)\" data-style=\"margin: 10px 0em; color: rgb(89, 89, 89); letter-spacing: 0.544px;\" style=\"-webkit-tap-highlight-color: transparent;margin: 10px 0em;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;color: rgb(89, 89, 89);letter-spacing: 0.544px;\"><section data-darkmode-bgcolor=\"rgb(36, 36, 36)\" data-darkmode-original-bgcolor=\"rgb(255, 255, 255)\" data-darkmode-color=\"rgb(138, 138, 138)\" data-darkmode-original-color=\"rgb(89, 89, 89)\" style=\"-webkit-tap-highlight-color: transparent;margin: 0px 0em;padding: 10px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;display: inline-block;width: 556px;border-width: 2px;border-style: dotted;border-color: rgb(192, 200, 209);\"><section powered-by=\"xiumi.us\" data-darkmode-bgcolor=\"rgb(36, 36, 36)\" data-darkmode-original-bgcolor=\"rgb(255, 255, 255)\" data-darkmode-color=\"rgb(138, 138, 138)\" data-darkmode-original-color=\"rgb(89, 89, 89)\" style=\"-webkit-tap-highlight-color: transparent;margin: 0px 0em;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;font-size: 13px;line-height: 2;letter-spacing: 2px;\"><p data-darkmode-bgcolor=\"rgb(36, 36, 36)\" data-darkmode-original-bgcolor=\"rgb(255, 255, 255)\" data-darkmode-color=\"rgb(106, 104, 111)\" data-darkmode-original-color=\"rgb(106, 104, 111)\" style=\"-webkit-tap-highlight-color: transparent;margin: 0px 0em;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;clear: both;min-height: 1em;color: rgb(106, 104, 111);\"><span leaf=\"\" style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;\"><span textstyle=\"\" style=\"color: rgb(0, 122, 170);text-decoration: underline;\">1.&nbsp;</span><a style=\"-webkit-tap-highlight-color: rgba(0, 0, 0, 0);margin: 0px;padding: 0px;outline: 0px;text-decoration: none;-webkit-user-drag: none;cursor: default;max-width: 100%;color: rgb(0, 122, 170);box-sizing: border-box !important;overflow-wrap: break-word !important;\" href=\"https://mp.weixin.qq.com/s?__biz=MzU4MDUyMDQyNQ==&amp;mid=2247538511&amp;idx=1&amp;sn=c2f80c1f6143af7bfcaa661951d7bf23&amp;chksm=fd5787c9ca200edf9b861f51dcdc6e15a575488ab6be0f5b721ea27047cf3316e07ea33a9a8d&amp;token=343952052&amp;lang=zh_CN&amp;scene=21#wechat_redirect\" textvalue=\"我的私密学习小圈子，从0到1手撸企业实战项目~\" data-itemshowtype=\"undefined\" target=\"_blank\" linktype=\"text\" data-linktype=\"2\"><span textstyle=\"\" style=\"color: rgb(0, 122, 170);text-decoration: underline;\">我的私密学习小圈子，从0到1手撸企业实战项目~</span></a></span></p><p data-darkmode-bgcolor=\"rgb(36, 36, 36)\" data-darkmode-original-bgcolor=\"rgb(255, 255, 255)\" data-darkmode-color=\"rgb(138, 138, 138)\" data-darkmode-original-color=\"rgb(89, 89, 89)\" style=\"-webkit-tap-highlight-color: transparent;margin: 0px 0em;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;clear: both;min-height: 1em;\"><span style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;color: rgb(106, 104, 111);\"><span leaf=\"\" style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;\"><span textstyle=\"\" style=\"color: rgb(0, 122, 170);text-decoration: underline;\">2.&nbsp;</span><a style=\"color: rgb(0, 122, 170);\" href=\"https://mp.weixin.qq.com/s?__biz=MzU4MDUyMDQyNQ==&amp;mid=2247558151&amp;idx=1&amp;sn=01f60b542d68eadd91cb8faa40347cce&amp;scene=21#wechat_redirect\" textvalue=\"Spring AI + DeepSeek，10分钟快速构建本地化AI对话系统！\" data-itemshowtype=\"0\" target=\"_blank\" linktype=\"text\" data-linktype=\"2\"><span textstyle=\"\" style=\"color: rgb(0, 122, 170);text-decoration: underline;\">Spring AI + DeepSeek，10分钟快速构建本地化AI对话系统！</span></a></span></span></p><p data-darkmode-bgcolor=\"rgb(36, 36, 36)\" data-darkmode-original-bgcolor=\"rgb(255, 255, 255)\" data-darkmode-color=\"rgb(138, 138, 138)\" data-darkmode-original-color=\"rgb(89, 89, 89)\" style=\"-webkit-tap-highlight-color: transparent;margin: 0px 0em;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;clear: both;min-height: 1em;\"><span style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;color: rgb(106, 104, 111);\"><span leaf=\"\" style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;\"><span textstyle=\"\" style=\"color: rgb(0, 122, 170);text-decoration: underline;\">3.&nbsp;</span><a style=\"color: rgb(0, 122, 170);\" href=\"https://mp.weixin.qq.com/s?__biz=MzU4MDUyMDQyNQ==&amp;mid=2247558156&amp;idx=2&amp;sn=b3ac6e18d28d2d072ee20f92bfc69b69&amp;scene=21#wechat_redirect\" textvalue=\"抖音一面：二维码扫码登录原理\" data-itemshowtype=\"0\" target=\"_blank\" linktype=\"text\" data-linktype=\"2\"><span textstyle=\"\" style=\"color: rgb(0, 122, 170);text-decoration: underline;\">抖音一面：二维码扫码登录原理</span></a></span></span></p><p data-darkmode-bgcolor=\"rgb(36, 36, 36)\" data-darkmode-original-bgcolor=\"rgb(255, 255, 255)\" data-darkmode-color=\"rgb(106, 104, 111)\" data-darkmode-original-color=\"rgb(106, 104, 111)\" style=\"-webkit-tap-highlight-color: transparent;margin: 0px 0em;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;clear: both;min-height: 1em;color: rgb(106, 104, 111);\"><span leaf=\"\" style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;\"><span textstyle=\"\" style=\"color: rgb(0, 122, 170);text-decoration: underline;\">4.&nbsp;</span><a style=\"color: rgb(0, 122, 170);\" href=\"https://mp.weixin.qq.com/s?__biz=MzU4MDUyMDQyNQ==&amp;mid=2247558156&amp;idx=3&amp;sn=3acf4d7f2d9c0420297f0c2cfe56985c&amp;scene=21#wechat_redirect\" textvalue=\"面试被问分布式事务（2PC、3PC、TCC），这样解释没毛病！\" data-itemshowtype=\"11\" target=\"_blank\" linktype=\"text\" data-linktype=\"2\"><span textstyle=\"\" style=\"color: rgb(0, 122, 170);text-decoration: underline;\">面试被问分布式事务（2PC、3PC、TCC），这样解释没毛病！</span></a></span></p></section></section></section></section><section data-darkmode-bgcolor=\"rgb(36, 36, 36)\" data-darkmode-original-bgcolor=\"rgb(255, 255, 255)\" data-darkmode-color=\"rgb(168, 168, 168)\" data-darkmode-original-color=\"rgb(62, 62, 62)\" data-style=\"margin-right: 0.5em; margin-left: 0.5em; white-space: normal; font-family: -apple-system-font, system-ui, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; letter-spacing: 0px; word-spacing: 2px; color: rgb(62, 62, 62); text-align: center;\" nodeleaf=\"\" style=\"-webkit-tap-highlight-color: transparent;margin: 0px 0.5em;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;clear: both;min-height: 1em;white-space: normal;font-family: -apple-system-font, system-ui, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif;letter-spacing: 0px;word-spacing: 2px;color: rgb(62, 62, 62);text-align: center;\"><img data-src=\"https://mmbiz.qpic.cn/sz_mmbiz_gif/knmrNHnmCLEVGGmicJODkfibhcqyUwmTSC8CUvAMG78wPemfibvQ502uFs9jlziaLP50YcTs4rL9hQuzX32PAUOPHA/640?wx_fmt=gif&amp;wxfrom=5&amp;wx_lazy=1&amp;wx_co=1&amp;tp=webp\" alt=\"图片\" class=\"rich_pages wxw-img __bg_gif\" data-ratio=\"0.5555555555555556\" data-s=\"300,640\" data-type=\"gif\" data-w=\"639\" style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;vertical-align: bottom;height: auto !important;border-radius: 8px;background-position: center center;background-repeat: no-repeat;background-color: rgb(238, 237, 235);background-size: 22px;border-width: 1px;border-style: solid;border-color: rgb(238, 237, 235);visibility: visible !important;width: 620.977px !important;\" data-fileid=\"100015744\" data-imgfileid=\"100050505\"></section><pre data-style=\"letter-spacing: 0.544px; text-size-adjust: auto; word-spacing: 2px; color: rgb(89, 89, 89);\" style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;word-spacing: 2px;color: rgb(89, 89, 89);\"><p data-style=\"margin-top: 5px; margin-bottom: 5px; white-space: normal; color: rgb(62, 62, 62); letter-spacing: 0.544px; font-family: &quot;Helvetica Neue&quot;, Helvetica, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 14px; line-height: normal;\" style=\"-webkit-tap-highlight-color: transparent;margin: 5px 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;clear: both;min-height: 1em;white-space: normal;color: rgb(62, 62, 62);font-family: &quot;Helvetica Neue&quot;, Helvetica, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif;font-size: 14px;line-height: normal;\"><span style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;font-family: Avenir, -apple-system-font, 微软雅黑, sans-serif;color: rgb(120, 172, 254);font-size: 15px;\"></span></p></pre></pre></pre></pre></pre><pre data-style=\"letter-spacing: 0.544px; font-size: 16px; color: rgb(63, 63, 63); word-spacing: 1px; line-height: inherit;\" style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;letter-spacing: 0.544px;font-variant-ligatures: common-ligatures;font-weight: 700;orphans: 4;widows: 1;word-spacing: 1px;caret-color: rgb(255, 0, 0);color: rgb(63, 63, 63);line-height: inherit;\"><pre data-style=\"letter-spacing: 0.544px; text-size-adjust: auto; word-spacing: 2px; color: rgb(89, 89, 89);\" style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;word-spacing: 2px;color: rgb(89, 89, 89);\"><p data-style=\"margin-top: 5px; margin-bottom: 5px; white-space: normal; color: rgb(62, 62, 62); letter-spacing: 0.544px; font-family: &quot;Helvetica Neue&quot;, Helvetica, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 14px; line-height: normal;\" style=\"-webkit-tap-highlight-color: transparent;margin: 5px 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;clear: both;min-height: 1em;white-space: normal;color: rgb(62, 62, 62);font-family: &quot;Helvetica Neue&quot;, Helvetica, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif;font-size: 14px;line-height: normal;\"><span style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;font-family: Avenir, -apple-system-font, 微软雅黑, sans-serif;color: rgb(120, 172, 254);font-size: 15px;\"><span leaf=\"\" style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;\">最近面试BAT，整理一份面试资料</span></span><span style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;font-family: Avenir, -apple-system-font, 微软雅黑, sans-serif;color: rgb(61, 167, 66);font-size: 17px;\"><span leaf=\"\" style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;\">《</span><strong style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;\"><span leaf=\"\" style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;\">Java面试BATJ通关手册</span></strong><span leaf=\"\" style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;\">》</span></span><span style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;font-family: Avenir, -apple-system-font, 微软雅黑, sans-serif;color: rgb(120, 172, 254);font-size: 15px;\"><span leaf=\"\" style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;\">，覆盖了Java核心技术、JVM、Java并发、SSM、微服务、数据库、数据结构等等。</span></span></p><p data-style=\"margin-top: 15px; margin-bottom: 15px; letter-spacing: 0.544px; white-space: pre-line; line-height: 30px; color: rgb(74, 74, 74); font-family: Avenir, -apple-system-font, 微软雅黑, sans-serif;\" style=\"-webkit-tap-highlight-color: transparent;margin: 15px 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;clear: both;min-height: 1em;white-space: pre-line;line-height: 30px;color: rgb(74, 74, 74);font-family: Avenir, -apple-system-font, 微软雅黑, sans-serif;\"><span style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;color: rgb(120, 172, 254);font-size: 15px;\"><span leaf=\"\" style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;\">获取方式：点“</span></span><span style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;color: rgb(61, 167, 66);font-size: 18px;\"><span leaf=\"\" style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;\">在看</span></span><span style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;color: rgb(120, 172, 254);font-size: 15px;\"><span leaf=\"\" style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;\">”，关注公众号并回复&nbsp;</span></span><span style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;color: rgb(61, 167, 66);font-size: 18px;\"><span leaf=\"\" style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;\">Java</span></span><span style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;color: rgb(120, 172, 254);font-size: 15px;\"><span leaf=\"\" style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;\">&nbsp;领取，更多内容陆续奉上。</span></span></p></pre><pre style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;color: rgb(62, 62, 62);caret-color: rgb(60, 60, 60);\"><p data-tool=\"mdnice编辑器\" style=\"-webkit-tap-highlight-color: transparent;margin: 10px 0px;padding: 8px 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;clear: both;min-height: 1em;white-space: normal;font-family: -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif;text-align: right;line-height: 1.6;color: rgb(63, 63, 63);\"><span style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;text-align: left;font-size: 15px;color: rgba(0, 0, 0, 0.8);font-family: Optima-Regular, PingFangTC-light;\"><span leaf=\"\" style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;\">PS：因公众号平台更改了推送规则，如果不想错过内容，记得读完点一下</span></span><strong style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;text-align: left;color: rgba(0, 0, 0, 0.8);font-family: Optima-Regular, PingFangTC-light;font-size: 12px;\"><span leaf=\"\" style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;\">“</span><span style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;color: rgb(255, 0, 0);\"><span leaf=\"\" style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;\">在看</span></span><span leaf=\"\" style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;\">”</span></strong><span style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;text-align: left;font-size: 15px;color: rgba(0, 0, 0, 0.8);font-family: Optima-Regular, PingFangTC-light;\"><span leaf=\"\" style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;\">，加个</span></span><strong style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;text-align: left;color: rgba(0, 0, 0, 0.8);font-family: Optima-Regular, PingFangTC-light;font-size: 12px;\"><span leaf=\"\" style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;\">“</span><span style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;color: rgb(255, 0, 0);\"><span leaf=\"\" style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;\">星标</span></span><span leaf=\"\" style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;\">”</span></strong><span style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;text-align: left;font-size: 15px;color: rgba(0, 0, 0, 0.8);font-family: Optima-Regular, PingFangTC-light;\"><span leaf=\"\" style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;\">，这样每次新文章推送才会第一时间出现在你的订阅列表里。</span></span></p><p data-tool=\"mdnice编辑器\" style=\"-webkit-tap-highlight-color: transparent;margin: 10px 0px;padding: 8px 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;clear: both;min-height: 1em;white-space: normal;font-family: -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif;text-align: right;line-height: 1.6;color: rgb(63, 63, 63);\"><span style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;text-align: left;font-size: 15px;font-family: Optima-Regular, PingFangTC-light;visibility: visible;color: rgb(255, 0, 0);\"><span leaf=\"\" style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;\">点</span><strong style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;\"><span leaf=\"\" style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;\">“在看”</span></strong><span leaf=\"\" style=\"-webkit-tap-highlight-color: transparent;margin: 0px;padding: 0px;outline: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;\">支持小哈呀，谢谢啦</span></span></p></pre></pre></section><p style=\"display: none;\"><mp-style-type data-value=\"3\"></mp-style-type></p>", "msg_sn": "f0943796c1ae59241814dcd80f0d07d7", "msg_idx": 1, "msg_mid": 2247558187, "msg_title": "如何画出一张优秀的架构图（老鸟必备）", "msg_desc": null, "msg_link": "https://mp.weixin.qq.com/s/6nwCalRrXenASZRUGnGmFg", "msg_source_url": "https://www.quanxiaoha.com/column/", "msg_cover": "https://mmbiz.qpic.cn/sz_mmbiz_jpg/knmrNHnmCLE2wTd9jX9u0GWe2XtE2gG03fJ4OnWJ4FqAkp7abJ5BP5hInj2UvIk3w8SLlYo8vr58tj849psvyA/0?wx_fmt=jpeg", "msg_article_type": null, "msg_publish_time": "2025-03-25T06:18:47.000Z", "msg_publish_time_str": "2025/03/25 14:18:47", "msg_type": "post"}}