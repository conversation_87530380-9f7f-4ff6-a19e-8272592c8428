{"code": 0, "done": true, "data": {"account_name": "阿清聊架构", "account_alias": null, "account_avatar": "http://wx.qlogo.cn/mmhead/FrdAUicrPIibfsSLrrTa9hxhfnqM9MacoxRACzsGesic2Zia8CPexNggHlwMD4KCt6M5XxMSmRxcUHc/132", "account_description": "把肚子里的那点技术货，彻底搞懂。", "account_id": "gh_e4bc3742c68d", "account_biz": "Mzk1NzMwOTM4NA==", "account_biz_number": **********, "account_qr_code": "https://open.weixin.qq.com/qr/code?username=gh_e4bc3742c68d", "msg_has_copyright": true, "msg_content": "<section style=\"line-height: normal;text-align: center;margin-top: 8px;margin-bottom: 8px;\"><span style=\"font-size: 16px;\"><span leaf=\"\">你好，我是阿清~</span></span></section><section style=\"line-height: normal;text-align: center;margin-top: 8px;margin-bottom: 8px;\"><span style=\"font-size: 16px;\"><span leaf=\"\"><img class=\"rich_pages wxw-img __bg_gif\" data-imgfileid=\"*********\" data-ratio=\"0.*****************\" data-src=\"https://mmbiz.qpic.cn/mmbiz_gif/oZia9K2yJPLa7WZzlwb1oMufu3ia6d07kg3tmuhpmIyrlFB4CtzG4fsbBHLF9I4kgTGDGcc5tDD2wMWrV4eCz8eA/640?wx_fmt=gif&amp;wxfrom=13&amp;tp=wxpic\" data-type=\"gif\" data-w=\"22\" style=\"outline: 0px;visibility: visible !important;width: 22px !important;\"></span></span></section><blockquote><p><span leaf=\"\"><span textstyle=\"\" style=\"font-size: 16px;font-weight: bold;\">《技术与管理100篇总结》</span></span></p><p><span leaf=\"\">01：如何画出有结构的架构图？</span></p></blockquote><p style=\"text-align: left;margin-bottom: 0px;margin-top: 0px;line-height: 1.5em;\"><span leaf=\"\"><br></span></p><p><span leaf=\"\"><span textstyle=\"\" style=\"font-size: 16px;\">我在十几年的开发经历中，最苦恼的是没有人教怎么画架构图，也没有课程可以学习。到现在，我画过的图也差不多有大几百张了，有那么些自己的心得，在此分享下。</span></span></p><p><span leaf=\"\"><span textstyle=\"\" style=\"font-size: 16px;\">本文包括以下内容：</span></span></p><ul style=\"list-style-type: disc;\" class=\"list-paddingleft-1\"><li><p><span leaf=\"\"><span textstyle=\"\" style=\"font-size: 16px;\">架构图的三要素</span></span></p></li><li><p><span leaf=\"\"><span textstyle=\"\" style=\"font-size: 16px;\">三层、三线、三色</span></span></p></li><li><p><span leaf=\"\"><span textstyle=\"\" style=\"font-size: 16px;\">符合心理学的线条</span></span></p></li><li><p><span leaf=\"\"><span textstyle=\"\" style=\"font-size: 16px;\">如何排出有结构的图</span></span></p><p><span leaf=\"\"><br></span></p></li></ul><p><span leaf=\"\"><span textstyle=\"\" style=\"font-size: 16px;color: #407600;font-weight: bold;text-decoration: underline;\">1.架构图的三要素</span></span></p><p><span leaf=\"\"><span textstyle=\"\" style=\"font-size: 16px;\">首先，一张架构图主要由组件、关系和边界组成。具体而言，要关注的要素有三个：分层、边界、连线。</span></span></p><p><span leaf=\"\"><span textstyle=\"\" style=\"font-size: 16px;font-weight: bold;\">分层</span><span textstyle=\"\" style=\"font-size: 16px;\">是结构化思维的核心，有分层才有分类。分层包括水平分层和纵深分层。水平分层，表示前后顺序关系的，两层之间要有可见的间隔空间。纵深分层，表示主次或总子关系。</span></span></p><p><span leaf=\"\"><span textstyle=\"\" style=\"font-size: 16px;font-weight: bold;\">边界</span><span textstyle=\"\" style=\"font-size: 16px;\">是指各层之间要有直接的边界线，同一层的不同类型也要有清晰的边界。</span></span></p><p><span leaf=\"\"><span textstyle=\"\" style=\"font-size: 16px;font-weight: bold;\">连线</span><span textstyle=\"\" style=\"font-size: 16px;\">在设计图中表示两个或两类组件的关系，常用的有</span></span><span leaf=\"\" data-pm-slice=\"1 1 [&quot;para&quot;,{&quot;tagName&quot;:&quot;p&quot;,&quot;attributes&quot;:{},&quot;namespaceURI&quot;:&quot;http://www.w3.org/1999/xhtml&quot;}]\"><span textstyle=\"\" style=\"font-size: 16px;\">直线条和</span></span><span leaf=\"\" data-pm-slice=\"1 1 [&quot;para&quot;,{&quot;tagName&quot;:&quot;p&quot;,&quot;attributes&quot;:{},&quot;namespaceURI&quot;:&quot;http://www.w3.org/1999/xhtml&quot;}]\"><span textstyle=\"\" style=\"font-size: 16px;\">宽线条。直线条表示两个具体组件的关联关系。宽箭头表示两类组件之间的关联关系</span></span></p><p><span leaf=\"\"><span textstyle=\"\" style=\"font-size: 16px;color: #7b0c00;font-weight: bold;\">有结构的设计图一定是有总子分层、有清晰的边界、有直白的关联关系。</span></span></p><p><span leaf=\"\"><span textstyle=\"\" style=\"font-size: 16px;color: #407600;font-weight: bold;text-decoration: underline;\">2.三层、三色、三线</span></span></p><p><span leaf=\"\"><span textstyle=\"\" style=\"font-size: 16px;\">三层，是指一张图里面最多三层，包括水平和纵深。也就是说水平平铺最多三层，从上往下、从左往右、从外往里深度最多三层。</span></span></p><p><span leaf=\"\"><span textstyle=\"\" style=\"font-size: 16px;\">例如我一般分为L1、L2、L3，若超过，则需进一步抽象分类或增加子图。</span></span></p><p><span leaf=\"\"><span textstyle=\"\" style=\"font-size: 16px;\">三色，是指除了黑色与白色外，最多三种颜色。颜色从浅至深，越在底下的越浅。</span></span></p><p><span leaf=\"\"><span textstyle=\"\" style=\"font-size: 16px;\">三线，是指图里面最多三种线条。例如我一般使用的三种线条：实线表示物理边界（工程、分布单元）、虚线表示逻辑边界（业务、职责）、粗线用来凸显关键的边界、或关系线条。</span></span></p><p><span leaf=\"\"><span textstyle=\"\" style=\"font-size: 16px;color: #7b0c00;font-weight: bold;\">奥卡姆剃刀原则：非必要，不新增。</span></span></p><p><span leaf=\"\"><span textstyle=\"\" style=\"font-size: 16px;color: #407600;font-weight: bold;text-decoration: underline;\">3.符合心理学的线条</span></span></p><p><span leaf=\"\"><span textstyle=\"\" style=\"font-size: 16px;\">这一条的原则就是若非必要，使用垂直、水平线条。</span></span></p><p><span leaf=\"\"><span textstyle=\"\" style=\"font-size: 16px;\">从进化的角度来讲，大自然的绝大部分事物都是垂直和水平的，所以人对垂直和水平线条更容易感知。</span></span></p><p><span leaf=\"\"><span textstyle=\"\" style=\"font-size: 16px;\">格塔式心理学提到以下两个原则：</span></span></p><p><span leaf=\"\"><span textstyle=\"\" style=\"font-size: 16px;\">人倾向于将复杂信息简化垂直线和水平线更容易形成闭合图形，帮助大脑快速识别物体边界。</span></span></p><p><span leaf=\"\"><span textstyle=\"\" style=\"font-size: 16px;\">为简洁、稳定的形式。垂直和水平线构成的形状符合这一原则，更易于被感知。</span></span></p><p><span leaf=\"\"><span textstyle=\"\" style=\"font-size: 16px;\">总结：</span></span><span leaf=\"\"><span textstyle=\"\" style=\"font-size: 16px;color: #7b0c00;font-weight: bold;\">图是画给人看的。</span></span></p><p><span leaf=\"\"><span textstyle=\"\" style=\"font-size: 16px;color: #407600;font-weight: bold;text-decoration: underline;\">4.如何排出有结构的图</span></span></p><p><span leaf=\"\"><span textstyle=\"\" style=\"font-size: 16px;\">关于排版，我的心得是：居中排版、边界对齐、间隔一致。</span></span></p><p><span leaf=\"\"><span textstyle=\"\" style=\"font-size: 16px;\">首先，整个图的位置要居于水平、横向的中间，最核心的组件居于最中间、最显眼的位置。</span></span></p><p><span leaf=\"\"><span textstyle=\"\" style=\"font-size: 16px;\">其次，不同层之间的左右边界、上下边界要对齐。同一层不同类别之间的边界也要对齐。</span></span></p><p><span leaf=\"\"><span textstyle=\"\" style=\"font-size: 16px;\">最后，如果有多层，每一层之间的间隔要一致，处于同一层的组件之间的间隔要一致。</span></span></p><p><span leaf=\"\"><span textstyle=\"\" style=\"font-size: 16px;\">谨记：</span><span textstyle=\"\" style=\"font-size: 16px;color: #7b0c00;font-weight: bold;\">好的设计图不一定漂亮，但结构一定清晰</span><span textstyle=\"\" style=\"font-size: 16px;\">。</span></span></p><p><span leaf=\"\"><span textstyle=\"\" style=\"font-size: 16px;color: #407600;font-weight: bold;text-decoration: underline;\">5.两张示例图</span></span></p><p><span leaf=\"\"><span textstyle=\"\" style=\"font-size: 16px;\">下面两张是我随便找的图，做了脱敏。一张是应用架构图，一张是部署架构图。</span></span></p><section style=\"text-align: center;\" nodeleaf=\"\"><img class=\"rich_pages wxw-img\" data-imgfileid=\"100000024\" data-s=\"300,640\" data-src=\"https://mmbiz.qpic.cn/mmbiz_png/h1D97C39H4LH7oJeduhh7W9yvk1wqtpibf0M3yXzHQoeonRaF02qzBFbU13umxMtVlLthbhuDf0wsiaObJhBtMlw/640?wx_fmt=png&amp;from=appmsg\" data-type=\"png\" type=\"block\"></section><section style=\"text-align: center;\" nodeleaf=\"\"><img class=\"rich_pages wxw-img\" data-imgfileid=\"100000025\" data-s=\"300,640\" data-src=\"https://mmbiz.qpic.cn/mmbiz_png/h1D97C39H4LH7oJeduhh7W9yvk1wqtpibFz4gkicicco7N1THJesuI6X8pXZmsu3lgr6ZP3B9ZYvKYWhvHfGFN3ZA/640?wx_fmt=png&amp;from=appmsg\" data-type=\"png\" type=\"block\"></section><p style=\"margin-bottom: 0px;margin-top: 0px;line-height: 1.5em;\"><span style=\"font-size: 16px;letter-spacing: 0.034em;\"><span leaf=\"\"><span textstyle=\"\" style=\"font-size: 16px;\">/</span></span></span></p><p style=\"-webkit-tap-highlight-color: transparent;outline: 0px;visibility: visible;margin-bottom: 0px;margin-top: 0px;line-height: 1.5em;\"><span leaf=\"\"><br></span></p><section style=\"-webkit-tap-highlight-color: transparent;outline: 0px;color: rgb(62, 62, 62);visibility: visible;margin-bottom: 8px;\"><span style=\"font-family: &quot;PingFang SC&quot;, system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif;letter-spacing: 0.578px;text-align: left;background-color: rgb(255, 255, 255);\"><span leaf=\"\" style=\"font-size: 16px;\"><span textstyle=\"\" style=\"font-size: 16px;\">有兴趣的话，点个</span></span><strong><span leaf=\"\" style=\"font-size: 16px;\"><span textstyle=\"\" style=\"font-size: 16px;\">关注和分享</span></span></strong><span leaf=\"\" style=\"font-size: 16px;\"><span textstyle=\"\" style=\"font-size: 16px;\">，与朋友们共勉！</span></span></span></section><section class=\"mp_profile_iframe_wrp\" nodeleaf=\"\"><mp-common-profile class=\"js_uneditable custom_select_card mp_profile_iframe\" data-pluginname=\"mpprofile\" data-nickname=\"阿清的AI转型之路\" data-from=\"0\" data-headimg=\"http://mmbiz.qpic.cn/mmbiz_png/h1D97C39H4LCvOTxZ1cJdc4qWbGaXQL9sIxMwHZBLn5Jc8tO0X9VknTbfMSy6mJfibQy3p6bGtPJG4UtXtOZePw/0?wx_fmt=png\" data-signature=\"把肚子里的那点技术货，彻底搞懂。\" data-id=\"Mzk1NzMwOTM4NA==\" data-is_biz_ban=\"0\" data-service_type=\"1\" data-verify_status=\"0\"></mp-common-profile></section><section style=\"-webkit-tap-highlight-color: transparent;outline: 0px;color: rgb(62, 62, 62);visibility: visible;margin-bottom: 8px;\"><span leaf=\"\"><br></span></section><p style=\"display: none;\"><mp-style-type data-value=\"3\"></mp-style-type></p>", "msg_author": "阿清", "msg_sn": "f97c8b9a85cf4aad59e64edd7afc1718", "msg_idx": 1, "msg_mid": 2247483677, "msg_title": "十年架构老兵画图心法，如何画出有结构的架构图？", "msg_desc": "你好，这是技术与管理100篇总结的第一篇。", "msg_link": "https://mp.weixin.qq.com/s/Gbo2wIvLgl1FbKyvC8LRhA", "msg_source_url": null, "msg_cover": "https://mmbiz.qpic.cn/mmbiz_jpg/h1D97C39H4LH7oJeduhh7W9yvk1wqtpibLTqiaYibzgk9qRu9xIcPIcsZtN5zDMSfxO4tJWh4Xpgj1y8ia30PLzsvQ/0?wx_fmt=jpeg", "msg_article_type": null, "msg_publish_time": "2025-06-26T15:02:35.000Z", "msg_publish_time_str": "2025/06/26 23:02:35", "msg_type": "post"}}