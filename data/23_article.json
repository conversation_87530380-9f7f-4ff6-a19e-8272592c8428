{"code": 0, "done": true, "data": {"account_name": "摸鱼网工的私塾", "account_alias": null, "account_avatar": "http://wx.qlogo.cn/mmhead/Q3auHgzwzM6Nk5ibqVW1ZMqKZmVyFt5W1CZHTABn4pjgK0wYEsribRrQ/132", "account_description": "摸鱼网工的日常~致力分享网工基础、计算机网络基础、Linux、路由器、交换机、等知识", "account_id": "gh_dac02144ca1a", "account_biz": "Mzg5NTgyMTQzNQ==", "account_biz_number": **********, "account_qr_code": "https://open.weixin.qq.com/qr/code?username=gh_dac02144ca1a", "msg_has_copyright": true, "msg_content": "<section data-tool=\"mdnice编辑器\" data-website=\"https://www.mdnice.com\" style=\"margin-top: 0px;margin-bottom: 0px;margin-left: 0px;margin-right: 0px;padding-top: 0px;padding-bottom: 0px;padding-left: 10px;padding-right: 10px;background-attachment: scroll;background-clip: border-box;background-color: rgba(0, 0, 0, 0);background-image: none;background-origin: padding-box;background-position-x: 0%;background-position-y: 0%;background-repeat: no-repeat;background-size: auto;width: auto;font-family: Optima, 'Microsoft YaHei', PingFangSC-regular, serif;font-size: 16px;color: rgb(0, 0, 0);line-height: 1.5em;word-spacing: 0em;letter-spacing: 0em;word-break: break-word;overflow-wrap: break-word;text-align: left;\" data-pm-slice=\"0 0 []\"><figure data-tool=\"mdnice编辑器\" style=\"margin-top: 10px;margin-bottom: 10px;margin-left: 0px;margin-right: 0px;padding-top: 0px;padding-bottom: 0px;padding-left: 0px;padding-right: 0px;display: flex;flex-direction: column;justify-content: center;align-items: center;\"><span leaf=\"\"><img class=\"rich_pages wxw-img\" data-imgfileid=\"100001638\" data-ratio=\"0.24500525762355416\" data-src=\"https://mmbiz.qpic.cn/sz_mmbiz_png/pBmd0hTCBLZVic487qWE8Emtf51Pd34B6ibI0JES4fJ2yUxhex1csFKJT0s0vM4YjJJg1T31qA0HREZzf4xaMsibQ/640?wx_fmt=png&amp;from=appmsg\" data-type=\"png\" data-w=\"951\" style=\"display: block;margin-top: 0px;margin-right: auto;margin-bottom: 0px;margin-left: auto;max-width: 100%;border-top-style: none;border-bottom-style: none;border-left-style: none;border-right-style: none;border-top-width: 3px;border-bottom-width: 3px;border-left-width: 3px;border-right-width: 3px;border-top-color: rgba(0, 0, 0, 0.4);border-bottom-color: rgba(0, 0, 0, 0.4);border-left-color: rgba(0, 0, 0, 0.4);border-right-color: rgba(0, 0, 0, 0.4);border-top-left-radius: 0px;border-top-right-radius: 0px;border-bottom-right-radius: 0px;border-bottom-left-radius: 0px;object-fit: fill;box-shadow: rgba(0, 0, 0, 0) 0px 0px 0px 0px;\"></span></figure><hr style=\"margin-top: 10px;margin-bottom: 10px;margin-left: 0px;margin-right: 0px;padding-top: 0px;padding-bottom: 0px;padding-left: 0px;padding-right: 0px;border-bottom-style: none;border-left-style: none;border-right-style: none;border-bottom-width: 3px;border-left-width: 3px;border-right-width: 3px;border-bottom-color: rgba(0, 0, 0, 0.4);border-left-color: rgba(0, 0, 0, 0.4);border-right-color: rgba(0, 0, 0, 0.4);border-top-left-radius: 0px;border-top-right-radius: 0px;border-bottom-right-radius: 0px;border-bottom-left-radius: 0px;background-attachment: scroll;background-clip: border-box;background-color: rgba(0, 0, 0, 0);background-image: none;background-origin: padding-box;background-position-x: 0%;background-position-y: 0%;background-repeat: no-repeat;background-size: auto;width: auto;height: 1px;border-top-width: 1px;border-top-style: dashed;border-top-color: rgb(221, 221, 221);\"><pre data-tool=\"mdnice编辑器\" style=\"border-radius: 5px;box-shadow: rgba(0, 0, 0, 0.55) 0px 2px 10px;text-align: left;margin-top: 10px;margin-bottom: 10px;margin-left: 0px;margin-right: 0px;padding-top: 0px;padding-bottom: 0px;padding-left: 0px;padding-right: 0px;\"><span data-cacheurl=\"\" data-remoteid=\"\" style=\"display: block;background: none;height: 30px;width: 100%;background-size: 40px;background-repeat: no-repeat;background-color: #282c34;margin-bottom: -7px;border-radius: 5px;background-position: 10px 10px;background-image: url(&quot;https://mmbiz.qpic.cn/mmbiz_svg/le1D2uwOTUMXiaeC3rDafwFdFxqv0Kt2usoD4V9F1jHLq1C7xb1mDhgYdUe5FbB8Pym5dfbmnZ3WjQv6szfW8PCz3b4EzQtZt/640?wx_fmt=svg&amp;from=appmsg&quot;);\"></span><code style=\"overflow-x: auto;padding: 16px;color: #abb2bf;padding-top: 15px;background: #282c34;border-radius: 5px;display: -webkit-box;font-family: Consolas, Monaco, Menlo, monospace;font-size: 12px;\"><span leaf=\"\">本文大概&nbsp;</span><span style=\"color: #d19a66;line-height: 26px;\"><span leaf=\"\">1705</span></span><span leaf=\"\">字，阅读需花&nbsp;</span><span style=\"color: #d19a66;line-height: 26px;\"><span leaf=\"\">5</span></span><span leaf=\"\">&nbsp;分钟</span><br><span leaf=\"\">内容不多，但也花了一些精力</span><br><span leaf=\"\">如要交流，欢迎评论区留言</span><br><span leaf=\"\">觉得不错的可以点小赞赞或者再看奥</span><br></code></pre><p data-tool=\"mdnice编辑器\" style=\"color: rgb(0, 0, 0);font-size: 16px;line-height: 1.8em;letter-spacing: 0em;text-align: left;text-indent: 0em;margin-top: 0px;margin-bottom: 0px;margin-left: 0px;margin-right: 0px;padding-top: 8px;padding-bottom: 8px;padding-left: 0px;padding-right: 0px;\"><span leaf=\"\">本期文章简要介绍数据中心网络架构的重要性及其对现代信息技术的影响</span><br><span leaf=\"\">在本篇开始前需要了解如下名词：</span></p><pre data-tool=\"mdnice编辑器\" style=\"border-radius: 5px;box-shadow: rgba(0, 0, 0, 0.55) 0px 2px 10px;text-align: left;margin-top: 10px;margin-bottom: 10px;margin-left: 0px;margin-right: 0px;padding-top: 0px;padding-bottom: 0px;padding-left: 0px;padding-right: 0px;\"><span data-cacheurl=\"\" data-remoteid=\"\" style=\"display: block;background: none;height: 30px;width: 100%;background-size: 40px;background-repeat: no-repeat;background-color: #282c34;margin-bottom: -7px;border-radius: 5px;background-position: 10px 10px;background-image: url(&quot;https://mmbiz.qpic.cn/mmbiz_svg/le1D2uwOTUMXiaeC3rDafwFdFxqv0Kt2usoD4V9F1jHLq1C7xb1mDhgYdUe5FbB8Pym5dfbmnZ3WjQv6szfW8PCz3b4EzQtZt/640?wx_fmt=svg&amp;from=appmsg&quot;);\"></span><code style=\"overflow-x: auto;padding: 16px;color: #abb2bf;padding-top: 15px;background: #282c34;border-radius: 5px;display: -webkit-box;font-family: Consolas, Monaco, Menlo, monospace;font-size: 12px;\"><span style=\"color: #e06c75;line-height: 26px;\"><span leaf=\"\">Clos</span></span><span leaf=\"\">网络（早期用于解决电话网络的一个组网模型）</span><br><span style=\"color: #e06c75;line-height: 26px;\"><span leaf=\"\">Spine</span></span><span leaf=\"\">（脊交换机，相当于传统三层组网里面的核心交换机）</span><br><span style=\"color: #e06c75;line-height: 26px;\"><span leaf=\"\">Leaf</span></span><span leaf=\"\">（叶交换机，相当于传统三层组网里面的接入交换机）</span><br><span leaf=\"\">南北向流量（数据中心内部服务器互访流量）</span><br><span leaf=\"\">东西向流量（数据中心内部外部往返流量）</span><br></code></pre><h2 data-tool=\"mdnice编辑器\" style=\"margin-top: 30px;margin-bottom: 15px;margin-left: 0px;margin-right: 0px;padding-top: 0px;padding-bottom: 0px;padding-left: 0px;padding-right: 0px;display: block;border-bottom-width: 2px;border-bottom-style: solid;border-bottom-color: rgb(110, 182, 255);font-size: 1.3em;\"><span style=\"display: none;\"></span><span style=\"font-size: 22px;color: rgb(255, 255, 255);line-height: 1.5em;letter-spacing: 0em;text-align: left;font-weight: bold;display: inline-block;background-image: initial;background-position-x: initial;background-position-y: initial;background-size: initial;background-repeat: initial;background-attachment: initial;background-origin: initial;background-clip: initial;background-color: rgb(110, 182, 255);padding-top: 3px;padding-right: 10px;padding-bottom: 1px;padding-left: 10px;border-top-right-radius: 3px;border-top-left-radius: 3px;margin-right: 3px;\"><span leaf=\"\">什么是Spine Leaf</span></span><span style=\"display: none;\"></span><span style=\"display: inline-block;vertical-align: bottom;border-bottom-width: 36px;border-bottom-style: solid;border-bottom-color: rgb(239, 235, 233);border-right-width: 20px;border-right-style: solid;border-right-color: transparent;\"></span></h2><figure data-tool=\"mdnice编辑器\" style=\"margin-top: 10px;margin-bottom: 10px;margin-left: 0px;margin-right: 0px;padding-top: 0px;padding-bottom: 0px;padding-left: 0px;padding-right: 0px;display: flex;flex-direction: column;justify-content: center;align-items: center;\"><span leaf=\"\"><img alt=\"Spine Leaf\" class=\"rich_pages wxw-img\" data-imgfileid=\"100001637\" data-ratio=\"0.37523452157598497\" data-src=\"https://mmbiz.qpic.cn/sz_mmbiz_png/pBmd0hTCBLZVic487qWE8Emtf51Pd34B6CLiclg6cmYphnbWLOInpQnkUUp6DcnmhqiaX9HNVE8q5HIcub8kQSAkw/640?wx_fmt=png&amp;from=appmsg\" data-type=\"png\" data-w=\"1066\" style=\"display: block;margin-top: 0px;margin-right: auto;margin-bottom: 0px;margin-left: auto;max-width: 100%;border-top-style: none;border-bottom-style: none;border-left-style: none;border-right-style: none;border-top-width: 3px;border-bottom-width: 3px;border-left-width: 3px;border-right-width: 3px;border-top-color: rgba(0, 0, 0, 0.4);border-bottom-color: rgba(0, 0, 0, 0.4);border-left-color: rgba(0, 0, 0, 0.4);border-right-color: rgba(0, 0, 0, 0.4);border-top-left-radius: 0px;border-top-right-radius: 0px;border-bottom-right-radius: 0px;border-bottom-left-radius: 0px;object-fit: fill;box-shadow: rgba(0, 0, 0, 0) 0px 0px 0px 0px;\"></span><figcaption style=\"color: rgb(136, 136, 136);font-size: 14px;line-height: 1.5em;letter-spacing: 0em;text-align: center;font-weight: normal;margin-top: 5px;margin-bottom: 0px;margin-left: 0px;margin-right: 0px;padding-top: 0px;padding-bottom: 0px;padding-left: 0px;padding-right: 0px;\"><span leaf=\"\">Spine Leaf</span></figcaption></figure><p data-tool=\"mdnice编辑器\" style=\"color: rgb(0, 0, 0);font-size: 16px;line-height: 1.8em;letter-spacing: 0em;text-align: left;text-indent: 0em;margin-top: 0px;margin-bottom: 0px;margin-left: 0px;margin-right: 0px;padding-top: 8px;padding-bottom: 8px;padding-left: 0px;padding-right: 0px;\"><span leaf=\"\">Spine叫脊，Leaf叫叶</span><br><span leaf=\"\">该网络架构也被称作脊叶网络架构</span><br><span leaf=\"\">一种两层设备的扁平化设计的网络架构</span><br><span leaf=\"\">在该组网架构中，所有的Leaf交换机都需要连接到Spine交换机上，形成全交叉连接，大幅提升网络可靠性</span><br><strong style=\"color: rgb(0, 0, 0);font-weight: bold;background-attachment: scroll;background-clip: border-box;background-color: rgba(0, 0, 0, 0);background-image: none;background-origin: padding-box;background-position-x: 0%;background-position-y: 0%;background-repeat: no-repeat;background-size: auto;width: auto;height: auto;margin-top: 0px;margin-bottom: 0px;margin-left: 0px;margin-right: 0px;padding-top: 0px;padding-bottom: 0px;padding-left: 0px;padding-right: 0px;border-top-style: none;border-bottom-style: none;border-left-style: none;border-right-style: none;border-top-width: 3px;border-bottom-width: 3px;border-left-width: 3px;border-right-width: 3px;border-top-color: rgba(0, 0, 0, 0.4);border-bottom-color: rgba(0, 0, 0, 0.4);border-left-color: rgba(0, 0, 0, 0.4);border-right-color: rgba(0, 0, 0, 0.4);border-top-left-radius: 0px;border-top-right-radius: 0px;border-bottom-right-radius: 0px;border-bottom-left-radius: 0px;\"><span leaf=\"\">Spine交换机：</span></strong><br><span leaf=\"\">相当于传统三层组网中的核心交换机，通常是一些大型框式交换机，支持板卡扩容一类的，用于连接Leaf交换机</span><br><strong style=\"color: rgb(0, 0, 0);font-weight: bold;background-attachment: scroll;background-clip: border-box;background-color: rgba(0, 0, 0, 0);background-image: none;background-origin: padding-box;background-position-x: 0%;background-position-y: 0%;background-repeat: no-repeat;background-size: auto;width: auto;height: auto;margin-top: 0px;margin-bottom: 0px;margin-left: 0px;margin-right: 0px;padding-top: 0px;padding-bottom: 0px;padding-left: 0px;padding-right: 0px;border-top-style: none;border-bottom-style: none;border-left-style: none;border-right-style: none;border-top-width: 3px;border-bottom-width: 3px;border-left-width: 3px;border-right-width: 3px;border-top-color: rgba(0, 0, 0, 0.4);border-bottom-color: rgba(0, 0, 0, 0.4);border-left-color: rgba(0, 0, 0, 0.4);border-right-color: rgba(0, 0, 0, 0.4);border-top-left-radius: 0px;border-top-right-radius: 0px;border-bottom-right-radius: 0px;border-bottom-left-radius: 0px;\"><span leaf=\"\">Leaf交换机：</span></strong><br><span leaf=\"\">相当于传统三层组网中的接入交换机，用于连接服务器、存储一类设备</span></p><figure data-tool=\"mdnice编辑器\" style=\"margin-top: 10px;margin-bottom: 10px;margin-left: 0px;margin-right: 0px;padding-top: 0px;padding-bottom: 0px;padding-left: 0px;padding-right: 0px;display: flex;flex-direction: column;justify-content: center;align-items: center;\"><span leaf=\"\"><img alt=\"CLOS网络\" class=\"rich_pages wxw-img\" data-imgfileid=\"100001635\" data-ratio=\"0.6591478696741855\" data-src=\"https://mmbiz.qpic.cn/sz_mmbiz_png/pBmd0hTCBLZVic487qWE8Emtf51Pd34B6JOxSV2rX9P0Mxicib7kz2k2ayGKDeG4N6LRjqBT1RkgfL6gfK9w7FiatA/640?wx_fmt=png&amp;from=appmsg\" data-type=\"png\" data-w=\"399\" style=\"display: block;margin-top: 0px;margin-right: auto;margin-bottom: 0px;margin-left: auto;max-width: 100%;border-top-style: none;border-bottom-style: none;border-left-style: none;border-right-style: none;border-top-width: 3px;border-bottom-width: 3px;border-left-width: 3px;border-right-width: 3px;border-top-color: rgba(0, 0, 0, 0.4);border-bottom-color: rgba(0, 0, 0, 0.4);border-left-color: rgba(0, 0, 0, 0.4);border-right-color: rgba(0, 0, 0, 0.4);border-top-left-radius: 0px;border-top-right-radius: 0px;border-bottom-right-radius: 0px;border-bottom-left-radius: 0px;object-fit: fill;box-shadow: rgba(0, 0, 0, 0) 0px 0px 0px 0px;\"></span><figcaption style=\"color: rgb(136, 136, 136);font-size: 14px;line-height: 1.5em;letter-spacing: 0em;text-align: center;font-weight: normal;margin-top: 5px;margin-bottom: 0px;margin-left: 0px;margin-right: 0px;padding-top: 0px;padding-bottom: 0px;padding-left: 0px;padding-right: 0px;\"><span leaf=\"\">CLOS网络</span></figcaption></figure><p data-tool=\"mdnice编辑器\" style=\"color: rgb(0, 0, 0);font-size: 16px;line-height: 1.8em;letter-spacing: 0em;text-align: left;text-indent: 0em;margin-top: 0px;margin-bottom: 0px;margin-left: 0px;margin-right: 0px;padding-top: 8px;padding-bottom: 8px;padding-left: 0px;padding-right: 0px;\"><span leaf=\"\">Spine Leaf的前生是CLOS网络</span><br><span leaf=\"\">从上图不难看出Clos网络压扁就是我们现在的Spine Leaf</span></p><h2 data-tool=\"mdnice编辑器\" style=\"margin-top: 30px;margin-bottom: 15px;margin-left: 0px;margin-right: 0px;padding-top: 0px;padding-bottom: 0px;padding-left: 0px;padding-right: 0px;display: block;border-bottom-width: 2px;border-bottom-style: solid;border-bottom-color: rgb(110, 182, 255);font-size: 1.3em;\"><span style=\"display: none;\"></span><span style=\"font-size: 22px;color: rgb(255, 255, 255);line-height: 1.5em;letter-spacing: 0em;text-align: left;font-weight: bold;display: inline-block;background-image: initial;background-position-x: initial;background-position-y: initial;background-size: initial;background-repeat: initial;background-attachment: initial;background-origin: initial;background-clip: initial;background-color: rgb(110, 182, 255);padding-top: 3px;padding-right: 10px;padding-bottom: 1px;padding-left: 10px;border-top-right-radius: 3px;border-top-left-radius: 3px;margin-right: 3px;\"><span leaf=\"\">为什么要采用Spine Leaf组网架构</span></span><span style=\"display: none;\"></span><span style=\"display: inline-block;vertical-align: bottom;border-bottom-width: 36px;border-bottom-style: solid;border-bottom-color: rgb(239, 235, 233);border-right-width: 20px;border-right-style: solid;border-right-color: transparent;\"></span></h2><p data-tool=\"mdnice编辑器\" style=\"color: rgb(0, 0, 0);font-size: 16px;line-height: 1.8em;letter-spacing: 0em;text-align: left;text-indent: 0em;margin-top: 0px;margin-bottom: 0px;margin-left: 0px;margin-right: 0px;padding-top: 8px;padding-bottom: 8px;padding-left: 0px;padding-right: 0px;\"><span leaf=\"\">首先该架构有如下几个优点：</span></p><ol style=\"list-style-type: decimal;margin-top: 8px;margin-bottom: 8px;margin-left: 0px;margin-right: 0px;padding-top: 0px;padding-bottom: 0px;padding-left: 25px;padding-right: 0px;color: rgb(0, 0, 0);\" class=\"list-paddingleft-1\"><li><section style=\"margin-top: 5px;margin-bottom: 5px;color: rgb(1, 1, 1);font-size: 16px;line-height: 1.8em;letter-spacing: 0em;text-align: left;font-weight: normal;\"><span leaf=\"\">高可靠性，当任意一个Leaf节点挂掉，不会影响整个网络结构失效</span></section></li><li><section style=\"margin-top: 5px;margin-bottom: 5px;color: rgb(1, 1, 1);font-size: 16px;line-height: 1.8em;letter-spacing: 0em;text-align: left;font-weight: normal;\"><span leaf=\"\">良好的扩展性，当Leaf资源不足时，通过新增Leaf交换机接入Spine，进行横向扩展</span></section></li><li><section style=\"margin-top: 5px;margin-bottom: 5px;color: rgb(1, 1, 1);font-size: 16px;line-height: 1.8em;letter-spacing: 0em;text-align: left;font-weight: normal;\"><span leaf=\"\">路径优化，任意2台服务器之间访问流量经过的路径，不会超过3台数据设备</span></section></li><li><section style=\"margin-top: 5px;margin-bottom: 5px;color: rgb(1, 1, 1);font-size: 16px;line-height: 1.8em;letter-spacing: 0em;text-align: left;font-weight: normal;\"><span leaf=\"\">在云化环境中，能有效应对虚机动态迁移带来的挑战</span></section></li></ol><p data-tool=\"mdnice编辑器\" style=\"color: rgb(0, 0, 0);font-size: 16px;line-height: 1.8em;letter-spacing: 0em;text-align: left;text-indent: 0em;margin-top: 0px;margin-bottom: 0px;margin-left: 0px;margin-right: 0px;padding-top: 8px;padding-bottom: 8px;padding-left: 0px;padding-right: 0px;\"><span leaf=\"\">目前在本人接触到的数据中心项目中，皆采用该架构组网，尤其是云数据中心，面向虚拟化云平台类的</span></p><h2 data-tool=\"mdnice编辑器\" style=\"margin-top: 30px;margin-bottom: 15px;margin-left: 0px;margin-right: 0px;padding-top: 0px;padding-bottom: 0px;padding-left: 0px;padding-right: 0px;display: block;border-bottom-width: 2px;border-bottom-style: solid;border-bottom-color: rgb(110, 182, 255);font-size: 1.3em;\"><span style=\"display: none;\"></span><span style=\"font-size: 22px;color: rgb(255, 255, 255);line-height: 1.5em;letter-spacing: 0em;text-align: left;font-weight: bold;display: inline-block;background-image: initial;background-position-x: initial;background-position-y: initial;background-size: initial;background-repeat: initial;background-attachment: initial;background-origin: initial;background-clip: initial;background-color: rgb(110, 182, 255);padding-top: 3px;padding-right: 10px;padding-bottom: 1px;padding-left: 10px;border-top-right-radius: 3px;border-top-left-radius: 3px;margin-right: 3px;\"><span leaf=\"\">与传统数据中心组网比较</span></span><span style=\"display: none;\"></span><span style=\"display: inline-block;vertical-align: bottom;border-bottom-width: 36px;border-bottom-style: solid;border-bottom-color: rgb(239, 235, 233);border-right-width: 20px;border-right-style: solid;border-right-color: transparent;\"></span></h2><figure data-tool=\"mdnice编辑器\" style=\"margin-top: 10px;margin-bottom: 10px;margin-left: 0px;margin-right: 0px;padding-top: 0px;padding-bottom: 0px;padding-left: 0px;padding-right: 0px;display: flex;flex-direction: column;justify-content: center;align-items: center;\"><span leaf=\"\"><img alt=\"传统三层网络架构\" class=\"rich_pages wxw-img\" data-imgfileid=\"100001634\" data-ratio=\"1.2484848484848485\" data-src=\"https://mmbiz.qpic.cn/sz_mmbiz_png/pBmd0hTCBLZVic487qWE8Emtf51Pd34B6K5KEsTsZ9ZSbJWCwZfftfgfPAeosSQ9ZmicjtXy6qJmFK4Nq9jic2yiaw/640?wx_fmt=png&amp;from=appmsg\" data-type=\"png\" data-w=\"330\" style=\"display: block;margin-top: 0px;margin-right: auto;margin-bottom: 0px;margin-left: auto;max-width: 100%;border-top-style: none;border-bottom-style: none;border-left-style: none;border-right-style: none;border-top-width: 3px;border-bottom-width: 3px;border-left-width: 3px;border-right-width: 3px;border-top-color: rgba(0, 0, 0, 0.4);border-bottom-color: rgba(0, 0, 0, 0.4);border-left-color: rgba(0, 0, 0, 0.4);border-right-color: rgba(0, 0, 0, 0.4);border-top-left-radius: 0px;border-top-right-radius: 0px;border-bottom-right-radius: 0px;border-bottom-left-radius: 0px;object-fit: fill;box-shadow: rgba(0, 0, 0, 0) 0px 0px 0px 0px;\"></span><figcaption style=\"color: rgb(136, 136, 136);font-size: 14px;line-height: 1.5em;letter-spacing: 0em;text-align: center;font-weight: normal;margin-top: 5px;margin-bottom: 0px;margin-left: 0px;margin-right: 0px;padding-top: 0px;padding-bottom: 0px;padding-left: 0px;padding-right: 0px;\"><span leaf=\"\">传统三层网络架构</span></figcaption></figure><p data-tool=\"mdnice编辑器\" style=\"color: rgb(0, 0, 0);font-size: 16px;line-height: 1.8em;letter-spacing: 0em;text-align: left;text-indent: 0em;margin-top: 0px;margin-bottom: 0px;margin-left: 0px;margin-right: 0px;padding-top: 8px;padding-bottom: 8px;padding-left: 0px;padding-right: 0px;\"><span leaf=\"\">在传统数据中心三层组网架构中，设备之间互访流量图可以如上图所示</span></p><blockquote style=\"margin-top: 20px;margin-bottom: 20px;margin-left: 0px;margin-right: 0px;padding-top: 10px;padding-bottom: 10px;padding-left: 20px;padding-right: 10px;border-top-style: none;border-bottom-style: none;border-left-style: solid;border-right-style: none;border-top-width: 3px;border-bottom-width: 3px;border-left-width: 3px;border-right-width: 3px;border-top-color: rgba(0, 0, 0, 0.4);border-bottom-color: rgba(0, 0, 0, 0.4);border-right-color: rgba(0, 0, 0, 0.4);border-top-left-radius: 0px;border-top-right-radius: 0px;border-bottom-right-radius: 0px;border-bottom-left-radius: 0px;width: auto;height: auto;box-shadow: rgba(0, 0, 0, 0) 0px 0px 0px 0px;display: block;overflow-x: auto;overflow-y: auto;border-left-color: rgb(239, 112, 96);background-image: initial;background-position-x: initial;background-position-y: initial;background-size: initial;background-repeat: initial;background-attachment: initial;background-origin: initial;background-clip: initial;background-color: rgb(255, 249, 249);\"><span style=\"display: none;color: rgb(0, 0, 0);font-size: 16px;line-height: 1.5em;letter-spacing: 0em;text-align: left;font-weight: normal;\"></span><p style=\"text-indent: 0em;padding-top: 8px;padding-bottom: 8px;padding-left: 0px;padding-right: 0px;color: rgb(0, 0, 0);font-size: 16px;line-height: 1.8em;letter-spacing: 0em;text-align: left;font-weight: normal;margin-top: 0px;margin-right: 0px;margin-bottom: 0px;margin-left: 0px;\"><strong style=\"color: rgb(0, 0, 0);font-weight: bold;background-attachment: scroll;background-clip: border-box;background-color: rgba(0, 0, 0, 0);background-image: none;background-origin: padding-box;background-position-x: 0%;background-position-y: 0%;background-repeat: no-repeat;background-size: auto;width: auto;height: auto;margin-top: 0px;margin-bottom: 0px;margin-left: 0px;margin-right: 0px;padding-top: 0px;padding-bottom: 0px;padding-left: 0px;padding-right: 0px;border-top-style: none;border-bottom-style: none;border-left-style: none;border-right-style: none;border-top-width: 3px;border-bottom-width: 3px;border-left-width: 3px;border-right-width: 3px;border-top-color: rgba(0, 0, 0, 0.4);border-bottom-color: rgba(0, 0, 0, 0.4);border-left-color: rgba(0, 0, 0, 0.4);border-right-color: rgba(0, 0, 0, 0.4);border-top-left-radius: 0px;border-top-right-radius: 0px;border-bottom-right-radius: 0px;border-bottom-left-radius: 0px;\"><span leaf=\"\">传统三层网络架构的特点</span></strong></p></blockquote><p data-tool=\"mdnice编辑器\" style=\"color: rgb(0, 0, 0);font-size: 16px;line-height: 1.8em;letter-spacing: 0em;text-align: left;text-indent: 0em;margin-top: 0px;margin-bottom: 0px;margin-left: 0px;margin-right: 0px;padding-top: 8px;padding-bottom: 8px;padding-left: 0px;padding-right: 0px;\"><strong style=\"color: rgb(0, 0, 0);font-weight: bold;background-attachment: scroll;background-clip: border-box;background-color: rgba(0, 0, 0, 0);background-image: none;background-origin: padding-box;background-position-x: 0%;background-position-y: 0%;background-repeat: no-repeat;background-size: auto;width: auto;height: auto;margin-top: 0px;margin-bottom: 0px;margin-left: 0px;margin-right: 0px;padding-top: 0px;padding-bottom: 0px;padding-left: 0px;padding-right: 0px;border-top-style: none;border-bottom-style: none;border-left-style: none;border-right-style: none;border-top-width: 3px;border-bottom-width: 3px;border-left-width: 3px;border-right-width: 3px;border-top-color: rgba(0, 0, 0, 0.4);border-bottom-color: rgba(0, 0, 0, 0.4);border-left-color: rgba(0, 0, 0, 0.4);border-right-color: rgba(0, 0, 0, 0.4);border-top-left-radius: 0px;border-top-right-radius: 0px;border-bottom-right-radius: 0px;border-bottom-left-radius: 0px;\"><span leaf=\"\">架构组成：</span></strong><span leaf=\"\">&nbsp;传统三层网络架构通常由接入层、汇聚层和核心层构成，接入层连接终端设备（如服务器），汇聚层负责流量的聚合以及策略控制，而核心层则作为数据中心内外通信的高速交换通道</span></p><p data-tool=\"mdnice编辑器\" style=\"color: rgb(0, 0, 0);font-size: 16px;line-height: 1.8em;letter-spacing: 0em;text-align: left;text-indent: 0em;margin-top: 0px;margin-bottom: 0px;margin-left: 0px;margin-right: 0px;padding-top: 8px;padding-bottom: 8px;padding-left: 0px;padding-right: 0px;\"><strong style=\"color: rgb(0, 0, 0);font-weight: bold;background-attachment: scroll;background-clip: border-box;background-color: rgba(0, 0, 0, 0);background-image: none;background-origin: padding-box;background-position-x: 0%;background-position-y: 0%;background-repeat: no-repeat;background-size: auto;width: auto;height: auto;margin-top: 0px;margin-bottom: 0px;margin-left: 0px;margin-right: 0px;padding-top: 0px;padding-bottom: 0px;padding-left: 0px;padding-right: 0px;border-top-style: none;border-bottom-style: none;border-left-style: none;border-right-style: none;border-top-width: 3px;border-bottom-width: 3px;border-left-width: 3px;border-right-width: 3px;border-top-color: rgba(0, 0, 0, 0.4);border-bottom-color: rgba(0, 0, 0, 0.4);border-left-color: rgba(0, 0, 0, 0.4);border-right-color: rgba(0, 0, 0, 0.4);border-top-left-radius: 0px;border-top-right-radius: 0px;border-bottom-right-radius: 0px;border-bottom-left-radius: 0px;\"><span leaf=\"\">东西向流量路径：</span></strong><span leaf=\"\">&nbsp;对于跨越三层的东西向流量访问，即数据中心内部服务器与外部网络之间的通信，数据包需要经过一个较长的路径，服务器&gt;接入&gt;汇聚&gt;核心&gt;汇聚&gt;接入&gt;服务器，流量需要经过5台设备才能到达被访服务器</span></p><p data-tool=\"mdnice编辑器\" style=\"color: rgb(0, 0, 0);font-size: 16px;line-height: 1.8em;letter-spacing: 0em;text-align: left;text-indent: 0em;margin-top: 0px;margin-bottom: 0px;margin-left: 0px;margin-right: 0px;padding-top: 8px;padding-bottom: 8px;padding-left: 0px;padding-right: 0px;\"><strong style=\"color: rgb(0, 0, 0);font-weight: bold;background-attachment: scroll;background-clip: border-box;background-color: rgba(0, 0, 0, 0);background-image: none;background-origin: padding-box;background-position-x: 0%;background-position-y: 0%;background-repeat: no-repeat;background-size: auto;width: auto;height: auto;margin-top: 0px;margin-bottom: 0px;margin-left: 0px;margin-right: 0px;padding-top: 0px;padding-bottom: 0px;padding-left: 0px;padding-right: 0px;border-top-style: none;border-bottom-style: none;border-left-style: none;border-right-style: none;border-top-width: 3px;border-bottom-width: 3px;border-left-width: 3px;border-right-width: 3px;border-top-color: rgba(0, 0, 0, 0.4);border-bottom-color: rgba(0, 0, 0, 0.4);border-left-color: rgba(0, 0, 0, 0.4);border-right-color: rgba(0, 0, 0, 0.4);border-top-left-radius: 0px;border-top-right-radius: 0px;border-bottom-right-radius: 0px;border-bottom-left-radius: 0px;\"><span leaf=\"\">南北向流量路径：</span></strong><span leaf=\"\">&nbsp;对于出局访问外部的流量也是，数据包需要一个较长的路径</span></p><p data-tool=\"mdnice编辑器\" style=\"color: rgb(0, 0, 0);font-size: 16px;line-height: 1.8em;letter-spacing: 0em;text-align: left;text-indent: 0em;margin-top: 0px;margin-bottom: 0px;margin-left: 0px;margin-right: 0px;padding-top: 8px;padding-bottom: 8px;padding-left: 0px;padding-right: 0px;\"><strong style=\"color: rgb(0, 0, 0);font-weight: bold;background-attachment: scroll;background-clip: border-box;background-color: rgba(0, 0, 0, 0);background-image: none;background-origin: padding-box;background-position-x: 0%;background-position-y: 0%;background-repeat: no-repeat;background-size: auto;width: auto;height: auto;margin-top: 0px;margin-bottom: 0px;margin-left: 0px;margin-right: 0px;padding-top: 0px;padding-bottom: 0px;padding-left: 0px;padding-right: 0px;border-top-style: none;border-bottom-style: none;border-left-style: none;border-right-style: none;border-top-width: 3px;border-bottom-width: 3px;border-left-width: 3px;border-right-width: 3px;border-top-color: rgba(0, 0, 0, 0.4);border-bottom-color: rgba(0, 0, 0, 0.4);border-left-color: rgba(0, 0, 0, 0.4);border-right-color: rgba(0, 0, 0, 0.4);border-top-left-radius: 0px;border-top-right-radius: 0px;border-bottom-right-radius: 0px;border-bottom-left-radius: 0px;\"><span leaf=\"\">二层流量路径：</span></strong><span leaf=\"\">&nbsp;对于接入层，如果有二层互访需求，那么二层互访流量至少也需经过三台设备才能到达目的地</span></p><figure data-tool=\"mdnice编辑器\" style=\"margin-top: 10px;margin-bottom: 10px;margin-left: 0px;margin-right: 0px;padding-top: 0px;padding-bottom: 0px;padding-left: 0px;padding-right: 0px;display: flex;flex-direction: column;justify-content: center;align-items: center;\"><span leaf=\"\"><img alt=\"Spine-Leaf\" class=\"rich_pages wxw-img\" data-imgfileid=\"100001636\" data-ratio=\"0.8449848024316109\" data-src=\"https://mmbiz.qpic.cn/sz_mmbiz_png/pBmd0hTCBLZVic487qWE8Emtf51Pd34B6poqxN79oKpBOUNW2rpp6yvnezGcB2iayLyKQA3F3TkWjWl6sQ3WCIdg/640?wx_fmt=png&amp;from=appmsg\" data-type=\"png\" data-w=\"329\" style=\"display: block;margin-top: 0px;margin-right: auto;margin-bottom: 0px;margin-left: auto;max-width: 100%;border-top-style: none;border-bottom-style: none;border-left-style: none;border-right-style: none;border-top-width: 3px;border-bottom-width: 3px;border-left-width: 3px;border-right-width: 3px;border-top-color: rgba(0, 0, 0, 0.4);border-bottom-color: rgba(0, 0, 0, 0.4);border-left-color: rgba(0, 0, 0, 0.4);border-right-color: rgba(0, 0, 0, 0.4);border-top-left-radius: 0px;border-top-right-radius: 0px;border-bottom-right-radius: 0px;border-bottom-left-radius: 0px;object-fit: fill;box-shadow: rgba(0, 0, 0, 0) 0px 0px 0px 0px;\"></span><figcaption style=\"color: rgb(136, 136, 136);font-size: 14px;line-height: 1.5em;letter-spacing: 0em;text-align: center;font-weight: normal;margin-top: 5px;margin-bottom: 0px;margin-left: 0px;margin-right: 0px;padding-top: 0px;padding-bottom: 0px;padding-left: 0px;padding-right: 0px;\"><span leaf=\"\">Spine-Leaf</span></figcaption></figure><p data-tool=\"mdnice编辑器\" style=\"color: rgb(0, 0, 0);font-size: 16px;line-height: 1.8em;letter-spacing: 0em;text-align: left;text-indent: 0em;margin-top: 0px;margin-bottom: 0px;margin-left: 0px;margin-right: 0px;padding-top: 8px;padding-bottom: 8px;padding-left: 0px;padding-right: 0px;\"><span leaf=\"\">在Spine-Leaf组网架构中，设备之间互访流量如上图所示</span></p><blockquote style=\"margin-top: 20px;margin-bottom: 20px;margin-left: 0px;margin-right: 0px;padding-top: 10px;padding-bottom: 10px;padding-left: 20px;padding-right: 10px;border-top-style: none;border-bottom-style: none;border-left-style: solid;border-right-style: none;border-top-width: 3px;border-bottom-width: 3px;border-left-width: 3px;border-right-width: 3px;border-top-color: rgba(0, 0, 0, 0.4);border-bottom-color: rgba(0, 0, 0, 0.4);border-right-color: rgba(0, 0, 0, 0.4);border-top-left-radius: 0px;border-top-right-radius: 0px;border-bottom-right-radius: 0px;border-bottom-left-radius: 0px;width: auto;height: auto;box-shadow: rgba(0, 0, 0, 0) 0px 0px 0px 0px;display: block;overflow-x: auto;overflow-y: auto;border-left-color: rgb(239, 112, 96);background-image: initial;background-position-x: initial;background-position-y: initial;background-size: initial;background-repeat: initial;background-attachment: initial;background-origin: initial;background-clip: initial;background-color: rgb(255, 249, 249);\"><span style=\"display: none;color: rgb(0, 0, 0);font-size: 16px;line-height: 1.5em;letter-spacing: 0em;text-align: left;font-weight: normal;\"></span><p style=\"text-indent: 0em;padding-top: 8px;padding-bottom: 8px;padding-left: 0px;padding-right: 0px;color: rgb(0, 0, 0);font-size: 16px;line-height: 1.8em;letter-spacing: 0em;text-align: left;font-weight: normal;margin-top: 0px;margin-right: 0px;margin-bottom: 0px;margin-left: 0px;\"><strong style=\"color: rgb(0, 0, 0);font-weight: bold;background-attachment: scroll;background-clip: border-box;background-color: rgba(0, 0, 0, 0);background-image: none;background-origin: padding-box;background-position-x: 0%;background-position-y: 0%;background-repeat: no-repeat;background-size: auto;width: auto;height: auto;margin-top: 0px;margin-bottom: 0px;margin-left: 0px;margin-right: 0px;padding-top: 0px;padding-bottom: 0px;padding-left: 0px;padding-right: 0px;border-top-style: none;border-bottom-style: none;border-left-style: none;border-right-style: none;border-top-width: 3px;border-bottom-width: 3px;border-left-width: 3px;border-right-width: 3px;border-top-color: rgba(0, 0, 0, 0.4);border-bottom-color: rgba(0, 0, 0, 0.4);border-left-color: rgba(0, 0, 0, 0.4);border-right-color: rgba(0, 0, 0, 0.4);border-top-left-radius: 0px;border-top-right-radius: 0px;border-bottom-right-radius: 0px;border-bottom-left-radius: 0px;\"><span leaf=\"\">Spine-Leaf架构的优势对比</span></strong></p></blockquote><p data-tool=\"mdnice编辑器\" style=\"color: rgb(0, 0, 0);font-size: 16px;line-height: 1.8em;letter-spacing: 0em;text-align: left;text-indent: 0em;margin-top: 0px;margin-bottom: 0px;margin-left: 0px;margin-right: 0px;padding-top: 8px;padding-bottom: 8px;padding-left: 0px;padding-right: 0px;\"><strong style=\"color: rgb(0, 0, 0);font-weight: bold;background-attachment: scroll;background-clip: border-box;background-color: rgba(0, 0, 0, 0);background-image: none;background-origin: padding-box;background-position-x: 0%;background-position-y: 0%;background-repeat: no-repeat;background-size: auto;width: auto;height: auto;margin-top: 0px;margin-bottom: 0px;margin-left: 0px;margin-right: 0px;padding-top: 0px;padding-bottom: 0px;padding-left: 0px;padding-right: 0px;border-top-style: none;border-bottom-style: none;border-left-style: none;border-right-style: none;border-top-width: 3px;border-bottom-width: 3px;border-left-width: 3px;border-right-width: 3px;border-top-color: rgba(0, 0, 0, 0.4);border-bottom-color: rgba(0, 0, 0, 0.4);border-left-color: rgba(0, 0, 0, 0.4);border-right-color: rgba(0, 0, 0, 0.4);border-top-left-radius: 0px;border-top-right-radius: 0px;border-bottom-right-radius: 0px;border-bottom-left-radius: 0px;\"><span leaf=\"\">简化路径：</span></strong><span leaf=\"\">在Spine-Leaf架构中，任意两台服务器之间的通信仅需通过一台Leaf交换机和一台Spine交换机即可完成，大大缩短了数据包的传输路径，减少了延迟</span></p><p data-tool=\"mdnice编辑器\" style=\"color: rgb(0, 0, 0);font-size: 16px;line-height: 1.8em;letter-spacing: 0em;text-align: left;text-indent: 0em;margin-top: 0px;margin-bottom: 0px;margin-left: 0px;margin-right: 0px;padding-top: 8px;padding-bottom: 8px;padding-left: 0px;padding-right: 0px;\"><strong style=\"color: rgb(0, 0, 0);font-weight: bold;background-attachment: scroll;background-clip: border-box;background-color: rgba(0, 0, 0, 0);background-image: none;background-origin: padding-box;background-position-x: 0%;background-position-y: 0%;background-repeat: no-repeat;background-size: auto;width: auto;height: auto;margin-top: 0px;margin-bottom: 0px;margin-left: 0px;margin-right: 0px;padding-top: 0px;padding-bottom: 0px;padding-left: 0px;padding-right: 0px;border-top-style: none;border-bottom-style: none;border-left-style: none;border-right-style: none;border-top-width: 3px;border-bottom-width: 3px;border-left-width: 3px;border-right-width: 3px;border-top-color: rgba(0, 0, 0, 0.4);border-bottom-color: rgba(0, 0, 0, 0.4);border-left-color: rgba(0, 0, 0, 0.4);border-right-color: rgba(0, 0, 0, 0.4);border-top-left-radius: 0px;border-top-right-radius: 0px;border-bottom-right-radius: 0px;border-bottom-left-radius: 0px;\"><span leaf=\"\">扩展性更强：</span></strong><span leaf=\"\">由于Spine-Leaf架构设计上的扁平化特性，它支持更高效的东西向扩展。当需要增加更多的服务器或存储资源时，只需添加更多的Leaf交换机并将其连接到现有的Spine交换机上即可，无需对现有网络进行重大调整</span></p><p data-tool=\"mdnice编辑器\" style=\"color: rgb(0, 0, 0);font-size: 16px;line-height: 1.8em;letter-spacing: 0em;text-align: left;text-indent: 0em;margin-top: 0px;margin-bottom: 0px;margin-left: 0px;margin-right: 0px;padding-top: 8px;padding-bottom: 8px;padding-left: 0px;padding-right: 0px;\"><strong style=\"color: rgb(0, 0, 0);font-weight: bold;background-attachment: scroll;background-clip: border-box;background-color: rgba(0, 0, 0, 0);background-image: none;background-origin: padding-box;background-position-x: 0%;background-position-y: 0%;background-repeat: no-repeat;background-size: auto;width: auto;height: auto;margin-top: 0px;margin-bottom: 0px;margin-left: 0px;margin-right: 0px;padding-top: 0px;padding-bottom: 0px;padding-left: 0px;padding-right: 0px;border-top-style: none;border-bottom-style: none;border-left-style: none;border-right-style: none;border-top-width: 3px;border-bottom-width: 3px;border-left-width: 3px;border-right-width: 3px;border-top-color: rgba(0, 0, 0, 0.4);border-bottom-color: rgba(0, 0, 0, 0.4);border-left-color: rgba(0, 0, 0, 0.4);border-right-color: rgba(0, 0, 0, 0.4);border-top-left-radius: 0px;border-top-right-radius: 0px;border-bottom-right-radius: 0px;border-bottom-left-radius: 0px;\"><span leaf=\"\">更高的可靠性：</span></strong><span leaf=\"\">即使某个Leaf节点出现故障，也不会影响整个网络的运行，因为每个Leaf交换机都是独立工作的，并且可以通过其他路径绕过故障点继续提供服务</span></p><p data-tool=\"mdnice编辑器\" style=\"color: rgb(0, 0, 0);font-size: 16px;line-height: 1.8em;letter-spacing: 0em;text-align: left;text-indent: 0em;margin-top: 0px;margin-bottom: 0px;margin-left: 0px;margin-right: 0px;padding-top: 8px;padding-bottom: 8px;padding-left: 0px;padding-right: 0px;\"><strong style=\"color: rgb(0, 0, 0);font-weight: bold;background-attachment: scroll;background-clip: border-box;background-color: rgba(0, 0, 0, 0);background-image: none;background-origin: padding-box;background-position-x: 0%;background-position-y: 0%;background-repeat: no-repeat;background-size: auto;width: auto;height: auto;margin-top: 0px;margin-bottom: 0px;margin-left: 0px;margin-right: 0px;padding-top: 0px;padding-bottom: 0px;padding-left: 0px;padding-right: 0px;border-top-style: none;border-bottom-style: none;border-left-style: none;border-right-style: none;border-top-width: 3px;border-bottom-width: 3px;border-left-width: 3px;border-right-width: 3px;border-top-color: rgba(0, 0, 0, 0.4);border-bottom-color: rgba(0, 0, 0, 0.4);border-left-color: rgba(0, 0, 0, 0.4);border-right-color: rgba(0, 0, 0, 0.4);border-top-left-radius: 0px;border-top-right-radius: 0px;border-bottom-right-radius: 0px;border-bottom-left-radius: 0px;\"><span leaf=\"\">优化的带宽利用率：</span></strong><span leaf=\"\">在Spine-Leaf架构下，所有链路都被充分利用，不存在传统三层架构中的某些链路负载过重而其他链路闲置的情况</span></p><figure data-tool=\"mdnice编辑器\" style=\"margin-top: 10px;margin-bottom: 10px;margin-left: 0px;margin-right: 0px;padding-top: 0px;padding-bottom: 0px;padding-left: 0px;padding-right: 0px;display: flex;flex-direction: column;justify-content: center;align-items: center;\"><span leaf=\"\"><img alt=\"架构\" class=\"rich_pages wxw-img\" data-imgfileid=\"100001642\" data-ratio=\"0.5355086372360844\" data-src=\"https://mmbiz.qpic.cn/sz_mmbiz_png/pBmd0hTCBLZVic487qWE8Emtf51Pd34B6mdwz2zyjicxSV2BPzvaq5ZENFQUDWicjetIfM2sp5ib2MUSLZofnBd2icQ/640?wx_fmt=png&amp;from=appmsg\" data-type=\"png\" data-w=\"1042\" style=\"display: block;margin-top: 0px;margin-right: auto;margin-bottom: 0px;margin-left: auto;max-width: 100%;border-top-style: none;border-bottom-style: none;border-left-style: none;border-right-style: none;border-top-width: 3px;border-bottom-width: 3px;border-left-width: 3px;border-right-width: 3px;border-top-color: rgba(0, 0, 0, 0.4);border-bottom-color: rgba(0, 0, 0, 0.4);border-left-color: rgba(0, 0, 0, 0.4);border-right-color: rgba(0, 0, 0, 0.4);border-top-left-radius: 0px;border-top-right-radius: 0px;border-bottom-right-radius: 0px;border-bottom-left-radius: 0px;object-fit: fill;box-shadow: rgba(0, 0, 0, 0) 0px 0px 0px 0px;\"></span><figcaption style=\"color: rgb(136, 136, 136);font-size: 14px;line-height: 1.5em;letter-spacing: 0em;text-align: center;font-weight: normal;margin-top: 5px;margin-bottom: 0px;margin-left: 0px;margin-right: 0px;padding-top: 0px;padding-bottom: 0px;padding-left: 0px;padding-right: 0px;\"><span leaf=\"\">架构</span></figcaption></figure><p data-tool=\"mdnice编辑器\" style=\"color: rgb(0, 0, 0);font-size: 16px;line-height: 1.8em;letter-spacing: 0em;text-align: left;text-indent: 0em;margin-top: 0px;margin-bottom: 0px;margin-left: 0px;margin-right: 0px;padding-top: 8px;padding-bottom: 8px;padding-left: 0px;padding-right: 0px;\"><span leaf=\"\">如上图所示，如果要扩展，一对Spine下可以扩展多组Leaf</span></p><blockquote style=\"margin-top: 20px;margin-bottom: 20px;margin-left: 0px;margin-right: 0px;padding-top: 10px;padding-bottom: 10px;padding-left: 20px;padding-right: 10px;border-top-style: none;border-bottom-style: none;border-left-style: solid;border-right-style: none;border-top-width: 3px;border-bottom-width: 3px;border-left-width: 3px;border-right-width: 3px;border-top-color: rgba(0, 0, 0, 0.4);border-bottom-color: rgba(0, 0, 0, 0.4);border-right-color: rgba(0, 0, 0, 0.4);border-top-left-radius: 0px;border-top-right-radius: 0px;border-bottom-right-radius: 0px;border-bottom-left-radius: 0px;width: auto;height: auto;box-shadow: rgba(0, 0, 0, 0) 0px 0px 0px 0px;display: block;overflow-x: auto;overflow-y: auto;border-left-color: rgb(239, 112, 96);background-image: initial;background-position-x: initial;background-position-y: initial;background-size: initial;background-repeat: initial;background-attachment: initial;background-origin: initial;background-clip: initial;background-color: rgb(255, 249, 249);\"><span style=\"display: none;color: rgb(0, 0, 0);font-size: 16px;line-height: 1.5em;letter-spacing: 0em;text-align: left;font-weight: normal;\"></span><p style=\"text-indent: 0em;padding-top: 8px;padding-bottom: 8px;padding-left: 0px;padding-right: 0px;color: rgb(0, 0, 0);font-size: 16px;line-height: 1.8em;letter-spacing: 0em;text-align: left;font-weight: normal;margin-top: 0px;margin-right: 0px;margin-bottom: 0px;margin-left: 0px;\"><strong style=\"color: rgb(0, 0, 0);font-weight: bold;background-attachment: scroll;background-clip: border-box;background-color: rgba(0, 0, 0, 0);background-image: none;background-origin: padding-box;background-position-x: 0%;background-position-y: 0%;background-repeat: no-repeat;background-size: auto;width: auto;height: auto;margin-top: 0px;margin-bottom: 0px;margin-left: 0px;margin-right: 0px;padding-top: 0px;padding-bottom: 0px;padding-left: 0px;padding-right: 0px;border-top-style: none;border-bottom-style: none;border-left-style: none;border-right-style: none;border-top-width: 3px;border-bottom-width: 3px;border-left-width: 3px;border-right-width: 3px;border-top-color: rgba(0, 0, 0, 0.4);border-bottom-color: rgba(0, 0, 0, 0.4);border-left-color: rgba(0, 0, 0, 0.4);border-right-color: rgba(0, 0, 0, 0.4);border-top-left-radius: 0px;border-top-right-radius: 0px;border-bottom-right-radius: 0px;border-bottom-left-radius: 0px;\"><span leaf=\"\">小知识：南北向 ≠ 所有跨层流量</span></strong></p></blockquote><p data-tool=\"mdnice编辑器\" style=\"color: rgb(0, 0, 0);font-size: 16px;line-height: 1.8em;letter-spacing: 0em;text-align: left;text-indent: 0em;margin-top: 0px;margin-bottom: 0px;margin-left: 0px;margin-right: 0px;padding-top: 8px;padding-bottom: 8px;padding-left: 0px;padding-right: 0px;\"><span leaf=\"\">很多同学刚开始学习数据中心架构时容易混淆“南北向”和“东西向”的定义，记住一句话：</span></p><p data-tool=\"mdnice编辑器\" style=\"color: rgb(0, 0, 0);font-size: 16px;line-height: 1.8em;letter-spacing: 0em;text-align: left;text-indent: 0em;margin-top: 0px;margin-bottom: 0px;margin-left: 0px;margin-right: 0px;padding-top: 8px;padding-bottom: 8px;padding-left: 0px;padding-right: 0px;\"><strong style=\"color: rgb(0, 0, 0);font-weight: bold;background-attachment: scroll;background-clip: border-box;background-color: rgba(0, 0, 0, 0);background-image: none;background-origin: padding-box;background-position-x: 0%;background-position-y: 0%;background-repeat: no-repeat;background-size: auto;width: auto;height: auto;margin-top: 0px;margin-bottom: 0px;margin-left: 0px;margin-right: 0px;padding-top: 0px;padding-bottom: 0px;padding-left: 0px;padding-right: 0px;border-top-style: none;border-bottom-style: none;border-left-style: none;border-right-style: none;border-top-width: 3px;border-bottom-width: 3px;border-left-width: 3px;border-right-width: 3px;border-top-color: rgba(0, 0, 0, 0.4);border-bottom-color: rgba(0, 0, 0, 0.4);border-left-color: rgba(0, 0, 0, 0.4);border-right-color: rgba(0, 0, 0, 0.4);border-top-left-radius: 0px;border-top-right-radius: 0px;border-bottom-right-radius: 0px;border-bottom-left-radius: 0px;\"><span leaf=\"\">“进出来回是南北，内部互访是东西”</span></strong></p><p data-tool=\"mdnice编辑器\" style=\"color: rgb(0, 0, 0);font-size: 16px;line-height: 1.8em;letter-spacing: 0em;text-align: left;text-indent: 0em;margin-top: 0px;margin-bottom: 0px;margin-left: 0px;margin-right: 0px;padding-top: 8px;padding-bottom: 8px;padding-left: 0px;padding-right: 0px;\"><span leaf=\"\">无论是否跨层，只要流量是在数据中心内部服务器之间流动，就是</span><strong style=\"color: rgb(0, 0, 0);font-weight: bold;background-attachment: scroll;background-clip: border-box;background-color: rgba(0, 0, 0, 0);background-image: none;background-origin: padding-box;background-position-x: 0%;background-position-y: 0%;background-repeat: no-repeat;background-size: auto;width: auto;height: auto;margin-top: 0px;margin-bottom: 0px;margin-left: 0px;margin-right: 0px;padding-top: 0px;padding-bottom: 0px;padding-left: 0px;padding-right: 0px;border-top-style: none;border-bottom-style: none;border-left-style: none;border-right-style: none;border-top-width: 3px;border-bottom-width: 3px;border-left-width: 3px;border-right-width: 3px;border-top-color: rgba(0, 0, 0, 0.4);border-bottom-color: rgba(0, 0, 0, 0.4);border-left-color: rgba(0, 0, 0, 0.4);border-right-color: rgba(0, 0, 0, 0.4);border-top-left-radius: 0px;border-top-right-radius: 0px;border-bottom-right-radius: 0px;border-bottom-left-radius: 0px;\"><span leaf=\"\">东西向流量</span></strong><span leaf=\"\">；只有当流量从外部进入或离开数据中心时，才是</span><strong style=\"color: rgb(0, 0, 0);font-weight: bold;background-attachment: scroll;background-clip: border-box;background-color: rgba(0, 0, 0, 0);background-image: none;background-origin: padding-box;background-position-x: 0%;background-position-y: 0%;background-repeat: no-repeat;background-size: auto;width: auto;height: auto;margin-top: 0px;margin-bottom: 0px;margin-left: 0px;margin-right: 0px;padding-top: 0px;padding-bottom: 0px;padding-left: 0px;padding-right: 0px;border-top-style: none;border-bottom-style: none;border-left-style: none;border-right-style: none;border-top-width: 3px;border-bottom-width: 3px;border-left-width: 3px;border-right-width: 3px;border-top-color: rgba(0, 0, 0, 0.4);border-bottom-color: rgba(0, 0, 0, 0.4);border-left-color: rgba(0, 0, 0, 0.4);border-right-color: rgba(0, 0, 0, 0.4);border-top-left-radius: 0px;border-top-right-radius: 0px;border-bottom-right-radius: 0px;border-bottom-left-radius: 0px;\"><span leaf=\"\">南北向流量</span></strong></p><h2 data-tool=\"mdnice编辑器\" style=\"margin-top: 30px;margin-bottom: 15px;margin-left: 0px;margin-right: 0px;padding-top: 0px;padding-bottom: 0px;padding-left: 0px;padding-right: 0px;display: block;border-bottom-width: 2px;border-bottom-style: solid;border-bottom-color: rgb(110, 182, 255);font-size: 1.3em;\"><span style=\"display: none;\"></span><span style=\"font-size: 22px;color: rgb(255, 255, 255);line-height: 1.5em;letter-spacing: 0em;text-align: left;font-weight: bold;display: inline-block;background-image: initial;background-position-x: initial;background-position-y: initial;background-size: initial;background-repeat: initial;background-attachment: initial;background-origin: initial;background-clip: initial;background-color: rgb(110, 182, 255);padding-top: 3px;padding-right: 10px;padding-bottom: 1px;padding-left: 10px;border-top-right-radius: 3px;border-top-left-radius: 3px;margin-right: 3px;\"><span leaf=\"\">未来趋势</span></span><span style=\"display: none;\"></span><span style=\"display: inline-block;vertical-align: bottom;border-bottom-width: 36px;border-bottom-style: solid;border-bottom-color: rgb(239, 235, 233);border-right-width: 20px;border-right-style: solid;border-right-color: transparent;\"></span></h2><p data-tool=\"mdnice编辑器\" style=\"color: rgb(0, 0, 0);font-size: 16px;line-height: 1.8em;letter-spacing: 0em;text-align: left;text-indent: 0em;margin-top: 0px;margin-bottom: 0px;margin-left: 0px;margin-right: 0px;padding-top: 8px;padding-bottom: 8px;padding-left: 0px;padding-right: 0px;\"><span leaf=\"\">随着5G-A、还有物联网（IoT）等技术的发展，数据中心将面临前所未有的高带宽需求，预计未来的Spine和Leaf交换机将支持更高速率的端口，如400Gbps甚至800Gbps，以满足日益增长的数据传输要求，目前我接触到量小标配的40Gpbs，大点是的100Gpbs，但是后续会接着扩容更高带宽的交换板 其次，SDN技术与Spine-Leaf架构的结合将进一步增强网络管理的灵活性和自动化水平，通过部署SDN控制器，维护方可以动态地调整流量路径，优化网络性能，并快速响应业务需求的变化，这不仅提高了效率，还降低了运维成本</span></p><figure data-tool=\"mdnice编辑器\" style=\"margin-top: 10px;margin-bottom: 10px;margin-left: 0px;margin-right: 0px;padding-top: 0px;padding-bottom: 0px;padding-left: 0px;padding-right: 0px;display: flex;flex-direction: column;justify-content: center;align-items: center;\"><span leaf=\"\"><img class=\"rich_pages wxw-img\" data-imgfileid=\"100001641\" data-ratio=\"0.37294685990338167\" data-src=\"https://mmbiz.qpic.cn/sz_mmbiz_png/pBmd0hTCBLZVic487qWE8Emtf51Pd34B6pkHQIVDwmgIpNF4gPMbcdHuP1t3Rf63z6yTPz9kKNTo64zQtbEJmJg/640?wx_fmt=png&amp;from=appmsg\" data-type=\"png\" data-w=\"1035\" style=\"display: block;margin-top: 0px;margin-right: auto;margin-bottom: 0px;margin-left: auto;max-width: 100%;border-top-style: none;border-bottom-style: none;border-left-style: none;border-right-style: none;border-top-width: 3px;border-bottom-width: 3px;border-left-width: 3px;border-right-width: 3px;border-top-color: rgba(0, 0, 0, 0.4);border-bottom-color: rgba(0, 0, 0, 0.4);border-left-color: rgba(0, 0, 0, 0.4);border-right-color: rgba(0, 0, 0, 0.4);border-top-left-radius: 0px;border-top-right-radius: 0px;border-bottom-right-radius: 0px;border-bottom-left-radius: 0px;object-fit: fill;box-shadow: rgba(0, 0, 0, 0) 0px 0px 0px 0px;\"></span></figure></section><section data-tool=\"mdnice编辑器\" data-website=\"https://www.mdnice.com\" style=\"margin-top: 0px;margin-bottom: 0px;margin-left: 0px;margin-right: 0px;padding-top: 0px;padding-bottom: 0px;padding-left: 10px;padding-right: 10px;background-attachment: scroll;background-clip: border-box;background-color: rgba(0, 0, 0, 0);background-image: none;background-origin: padding-box;background-position-x: 0%;background-position-y: 0%;background-repeat: no-repeat;background-size: auto;width: auto;font-family: Optima, 'Microsoft YaHei', PingFangSC-regular, serif;font-size: 16px;color: rgb(0, 0, 0);line-height: 1.5em;word-spacing: 0em;letter-spacing: 0em;word-break: break-word;overflow-wrap: break-word;text-align: left;\" data-pm-slice=\"3 3 []\"><p data-tool=\"mdnice编辑器\" style=\"color: rgb(0, 0, 0);font-size: 16px;line-height: 1.8em;letter-spacing: 0em;text-align: left;text-indent: 0em;margin-top: 0px;margin-bottom: 0px;margin-left: 0px;margin-right: 0px;padding-top: 8px;padding-bottom: 8px;padding-left: 0px;padding-right: 0px;\"><span leaf=\"\">往期内容：</span></p></section><section><span leaf=\"\"><a class=\"normal_text_link\" target=\"_blank\" style=\"\" href=\"https://mp.weixin.qq.com/s?__biz=Mzg5NTgyMTQzNQ==&amp;mid=2247485281&amp;idx=1&amp;sn=4f8500cb6991f2eda06356f825179ae0&amp;scene=21#wechat_redirect\" textvalue=\"数据中心EVPN 分布式网关基础实验\" data-itemshowtype=\"0\" linktype=\"text\" data-linktype=\"2\">数据中心EVPN 分布式网关基础实验</a></span></section><section><span leaf=\"\"><a class=\"normal_text_link\" target=\"_blank\" style=\"\" href=\"https://mp.weixin.qq.com/s?__biz=Mzg5NTgyMTQzNQ==&amp;mid=2247485249&amp;idx=1&amp;sn=adbb5c33ec09322d179adbace6f62ef5&amp;scene=21#wechat_redirect\" textvalue=\"数据中心技术EVPN讲解\" data-itemshowtype=\"0\" linktype=\"text\" data-linktype=\"2\">数据中心技术EVPN讲解</a></span></section><section><span leaf=\"\"><a class=\"normal_text_link\" target=\"_blank\" style=\"\" href=\"https://mp.weixin.qq.com/s?__biz=Mzg5NTgyMTQzNQ==&amp;mid=2247485150&amp;idx=1&amp;sn=ddb7e66364e9b65c7b74016336ea6e62&amp;scene=21#wechat_redirect\" textvalue=\"数据中心技术VXLAN集中式网关实验\" data-itemshowtype=\"0\" linktype=\"text\" data-linktype=\"2\">数据中心技术VXLAN集中式网关实验</a></span></section><section><span leaf=\"\"><a class=\"normal_text_link\" target=\"_blank\" style=\"\" href=\"https://mp.weixin.qq.com/s?__biz=Mzg5NTgyMTQzNQ==&amp;mid=2247485120&amp;idx=1&amp;sn=9d80f0a48b6b68de45aca112a29392a7&amp;scene=21#wechat_redirect\" textvalue=\"数据中心VXLAN技术讲解\" data-itemshowtype=\"0\" linktype=\"text\" data-linktype=\"2\">数据中心VXLAN技术讲解</a></span></section><section><span leaf=\"\"><a class=\"normal_text_link\" target=\"_blank\" style=\"\" href=\"https://mp.weixin.qq.com/s?__biz=Mzg5NTgyMTQzNQ==&amp;mid=2247485084&amp;idx=1&amp;sn=9f8a043be5f7e070a8a400f8a0ea0f77&amp;scene=21#wechat_redirect\" textvalue=\"数据中心技术M-LAG配置实验\" data-itemshowtype=\"0\" linktype=\"text\" data-linktype=\"2\">数据中心技术M-LAG配置实验</a></span></section><section><span leaf=\"\"><a class=\"normal_text_link\" target=\"_blank\" style=\"\" href=\"https://mp.weixin.qq.com/s?__biz=Mzg5NTgyMTQzNQ==&amp;mid=2247485037&amp;idx=1&amp;sn=31c36446b48a62295fe8929ec2cbd96b&amp;scene=21#wechat_redirect\" textvalue=\"数据中心M-LAG技术\" data-itemshowtype=\"0\" linktype=\"text\" data-linktype=\"2\">数据中心M-LAG技术</a></span></section><p style=\"display: none;\"><mp-style-type data-value=\"3\"></mp-style-type></p>", "msg_author": "Magic清凨", "msg_sn": "f4e24a0c540afe0b615cfd12f8a6ea51", "msg_idx": 1, "msg_mid": 2247485295, "msg_title": "数据中心Spine Leaf组网架构", "msg_desc": "数据中心Spine Leaf组网架构讲解", "msg_link": "https://mp.weixin.qq.com/s/JWG9llgJ-fCrn5KmezzujQ", "msg_source_url": null, "msg_cover": "https://mmbiz.qpic.cn/sz_mmbiz_jpg/pBmd0hTCBLZVic487qWE8Emtf51Pd34B6yP8ibLBuK3szELwH8yaqFe8UssGjD8bbnHNWakG1Tib3oBTkrvrx9ybA/0?wx_fmt=jpeg", "msg_article_type": null, "msg_publish_time": "2025-06-25T06:29:40.000Z", "msg_publish_time_str": "2025/06/25 14:29:40", "msg_type": "post"}}