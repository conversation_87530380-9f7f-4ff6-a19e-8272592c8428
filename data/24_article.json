{"code": 0, "done": true, "data": {"account_name": "mikechen的架构笔记", "account_alias": null, "account_avatar": "http://wx.qlogo.cn/mmhead/k8mFfEmdQe0BQicmGeeRMy8s5vtSvs2SsapEaA6PdicOGom5QLwJppciawqcwbXYI4ZgmRJxchicTNU/132", "account_description": "十余年BAT架构经验倾囊相授!", "account_id": "gh_66e4bc33b783", "account_biz": "MzI2MTMwMTkxMQ==", "account_biz_number": **********, "account_qr_code": "https://open.weixin.qq.com/qr/code?username=gh_66e4bc33b783", "msg_has_copyright": true, "msg_content": "<p style=\"margin-bottom: 0px;letter-spacing: 0.578px;text-align: center;\"><span style=\"letter-spacing: 0.578px;font-size: 12px;\"><span leaf=\"\">关注</span></span><span style=\"font-family: PingFangSC-Medium, &quot;PingFang SC&quot;;font-weight: 700;letter-spacing: 2px;widows: 1;background-color: rgb(255, 255, 255);font-size: 12px;color: rgb(0, 82, 255);\"><span leaf=\"\">△</span></span><strong><span style=\"letter-spacing: 0.578px;color: rgb(0, 128, 255);font-size: 12px;\"><span leaf=\"\">mikechen</span></span><span style=\"font-family: PingFangSC-Medium, &quot;PingFang SC&quot;;letter-spacing: 2px;widows: 1;background-color: rgb(255, 255, 255);font-size: 12px;color: rgb(0, 82, 255);\"><span leaf=\"\">△</span></span></strong><span style=\"letter-spacing: 0.578px;font-size: 12px;\"><span leaf=\"\">，</span><strong><span style=\"letter-spacing: 0.578px;color: rgb(255, 104, 39);\"><span leaf=\"\">十余年BAT架构经验倾囊相授！</span></span></strong></span></p><p style=\"text-align: center;margin-bottom: 0px;\"><a href=\"https://mp.weixin.qq.com/s?__biz=Mzg2NTg1NTQ2NQ==&amp;mid=2247500275&amp;idx=1&amp;sn=3c4d7ae5f524e012f2fb44fd2259ff4d&amp;chksm=ce513375f926ba63802e679bd2286b385f3709b62fadda6a1c3337d8dfc08e7e6eeb377a4156&amp;scene=21&amp;token=1498050498&amp;lang=zh_CN#wechat_redirect\" imgurl=\"https://mmbiz.qpic.cn/sz_mmbiz_png/WH9ojuibmKSov8ZFIBWVM4ppdZ7BzFWaRkcW3S1Eib02CbSRC349rFpNjFa9BkFVTQong4qMSgCxRl47RpLk2RBQ/640?wx_fmt=png&amp;from=appmsg\" linktype=\"image\" tab=\"innerlink\" target=\"_blank\" data-linktype=\"1\"><span class=\"js_jump_icon h5_image_link\"><img class=\"rich_pages wxw-img js_insertlocalimg\" data-imgfileid=\"100005693\" data-ratio=\"0.3333333333333333\" data-s=\"300,640\" data-src=\"https://mmbiz.qpic.cn/sz_mmbiz_png/WH9ojuibmKSov8ZFIBWVM4ppdZ7BzFWaRkcW3S1Eib02CbSRC349rFpNjFa9BkFVTQong4qMSgCxRl47RpLk2RBQ/640?wx_fmt=png&amp;from=appmsg\" data-type=\"png\" data-w=\"1080\"></span></a></p><p style=\"text-align: left;margin-bottom: 0px;\"><span leaf=\"\"><br></span></p><p style=\"margin-bottom: 0px;letter-spacing: 0.578px;\"><span style=\"font-size: 16px;\"><strong><span leaf=\"\">大家好，我是mikechen。</span></strong></span></p><p style=\"margin-bottom: 0px;letter-spacing: 0.578px;\"><span style=\"font-size: 16px;\"></span><span leaf=\"\"><br></span></p><p style=\"border: 0px;font-family: -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Microsoft YaHei&quot;, &quot;Source Han Sans SC&quot;, &quot;Noto Sans CJK SC&quot;, &quot;WenQuanYi Micro Hei&quot;, sans-serif;font-size: 16px;font-style: normal;font-weight: 400;margin: 0px 0px 1.7em;outline: 0px;padding: 0px;vertical-align: baseline;overflow-wrap: break-word;box-sizing: border-box;word-break: normal;line-height: 1.5;color: rgb(34, 34, 34);font-variant-ligatures: normal;font-variant-caps: normal;letter-spacing: normal;orphans: 2;text-align: start;text-indent: 0px;text-transform: none;widows: 2;word-spacing: 0px;-webkit-text-stroke-width: 0px;white-space: normal;background-color: rgb(255, 255, 255);text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;\" data-pm-slice=\"0 0 []\"><span leaf=\"\" data-pm-slice=\"1 1 [&quot;para&quot;,{&quot;tagName&quot;:&quot;p&quot;,&quot;attributes&quot;:{&quot;style&quot;:&quot;border: 0px; font-family: -apple-system, BlinkMacSystemFont, \\&quot;Helvetica Neue\\&quot;, \\&quot;PingFang SC\\&quot;, \\&quot;Microsoft YaHei\\&quot;, \\&quot;Source Han Sans SC\\&quot;, \\&quot;Noto Sans CJK SC\\&quot;, \\&quot;WenQuanYi Micro Hei\\&quot;, sans-serif; font-size: 16px; font-style: normal; font-weight: 400; margin: 0px 0px 1.7em; outline: 0px; padding: 0px; vertical-align: baseline; overflow-wrap: break-word; box-sizing: border-box; word-break: normal; line-height: 1.5; color: rgb(34, 34, 34); font-variant-ligatures: normal; font-variant-caps: normal; letter-spacing: normal; orphans: 2; text-align: start; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; background-color: rgb(255, 255, 255); text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial;&quot;,&quot;data-pm-slice&quot;:&quot;0 0 []&quot;},&quot;namespaceURI&quot;:&quot;http://www.w3.org/1999/xhtml&quot;}]\">微服务架构模式是大型架构的核心，下面我重点详解微服务架构模式@mikechen</span></p><p style=\"margin-bottom: 0px;letter-spacing: 0.578px;\"><span style=\"font-size: 16px;\"><span style=\"margin: 0px;padding: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;color: rgba(0, 0, 0, 0.9);font-style: normal;font-variant-ligatures: normal;font-variant-caps: normal;font-weight: 400;orphans: 2;text-indent: 0px;text-transform: none;widows: 2;word-spacing: 0px;-webkit-text-stroke-width: 0px;white-space: normal;text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;font-size: 16px;font-family: system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif;letter-spacing: 0.544px;text-align: left;background-color: rgb(255, 255, 255);visibility: visible;\"></span><span style=\"margin: 0px;padding: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;color: rgba(0, 0, 0, 0.9);font-style: normal;font-variant-ligatures: normal;font-variant-caps: normal;font-weight: 400;orphans: 2;text-indent: 0px;text-transform: none;widows: 2;word-spacing: 0px;-webkit-text-stroke-width: 0px;white-space: normal;text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;font-size: 16px;font-family: system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif;letter-spacing: 0.544px;text-align: left;background-color: rgb(255, 255, 255);visibility: visible;\"><span leaf=\"\">mikechen原创《</span><strong><span leaf=\"\"><a class=\"normal_text_link\" target=\"_blank\" style=\"\" href=\"https://mp.weixin.qq.com/s?__biz=Mzg2NTg1NTQ2NQ==&amp;mid=2247500275&amp;idx=1&amp;sn=3c4d7ae5f524e012f2fb44fd2259ff4d&amp;chksm=ce513375f926ba63802e679bd2286b385f3709b62fadda6a1c3337d8dfc08e7e6eeb377a4156&amp;scene=21&amp;token=1498050498&amp;lang=zh_CN#wechat_redirect\" textvalue=\"30万字阿里架构师进阶从0到1全部合集\" linktype=\"text\" data-linktype=\"2\">30万字阿里架构师进阶从0到1全部合集</a></span></strong><span leaf=\"\">》</span></span><span style=\"margin: 0px;padding: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;color: rgba(0, 0, 0, 0.9);font-style: normal;font-variant-ligatures: normal;font-variant-caps: normal;font-weight: 400;orphans: 2;text-indent: 0px;text-transform: none;widows: 2;word-spacing: 0px;-webkit-text-stroke-width: 0px;white-space: normal;text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;font-size: 16px;font-family: system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif;letter-spacing: 0.544px;text-align: left;background-color: rgb(255, 255, 255);visibility: visible;\"><span leaf=\"\">，请关注本公众号【mikechen的架构笔记】，后台回复：</span></span><span style=\"margin: 0px;padding: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;font-style: normal;font-variant-ligatures: normal;font-variant-caps: normal;font-weight: 400;orphans: 2;text-indent: 0px;text-transform: none;widows: 2;word-spacing: 0px;-webkit-text-stroke-width: 0px;white-space: normal;text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;font-size: 16px;font-family: system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif;letter-spacing: 0.544px;text-align: left;background-color: rgb(255, 255, 255);outline: 0px;color: rgb(217, 33, 66);visibility: visible;\"><strong style=\"margin: 0px;padding: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;outline: 0px;visibility: visible;\"><span leaf=\"\">架构</span></strong></span><span style=\"margin: 0px;padding: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;color: rgba(0, 0, 0, 0.9);font-style: normal;font-variant-ligatures: normal;font-variant-caps: normal;font-weight: 400;orphans: 2;text-indent: 0px;text-transform: none;widows: 2;word-spacing: 0px;-webkit-text-stroke-width: 0px;white-space: normal;text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;font-size: 16px;font-family: system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif;letter-spacing: 0.544px;text-align: left;background-color: rgb(255, 255, 255);visibility: visible;\"><span leaf=\"\">，即可领取。</span></span><span style=\"margin: 0px;padding: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;color: rgba(0, 0, 0, 0.9);font-style: normal;font-variant-ligatures: normal;font-variant-caps: normal;font-weight: 400;orphans: 2;text-indent: 0px;text-transform: none;widows: 2;word-spacing: 0px;-webkit-text-stroke-width: 0px;white-space: normal;text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;font-size: 16px;font-family: system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif;letter-spacing: 0.544px;text-align: left;background-color: rgb(255, 255, 255);visibility: visible;\"></span><span leaf=\"\"><br></span></span></p><p style=\"margin-bottom: 0px;letter-spacing: 0.578px;\"><span style=\"font-size: 16px;\"><span leaf=\"\"><br></span></span></p><p data-track=\"16\" style=\"border: 0px;font-family: -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Microsoft YaHei&quot;, &quot;Source Han Sans SC&quot;, &quot;Noto Sans CJK SC&quot;, &quot;WenQuanYi Micro Hei&quot;, sans-serif;font-size: 16px;font-style: normal;font-weight: 400;margin: 0px 0px 1.7em;outline: 0px;padding: 0px;vertical-align: baseline;overflow-wrap: break-word;box-sizing: border-box;word-break: normal;line-height: 1.5;color: rgb(34, 34, 34);font-variant-ligatures: normal;font-variant-caps: normal;letter-spacing: normal;orphans: 2;text-align: start;text-indent: 0px;text-transform: none;widows: 2;word-spacing: 0px;-webkit-text-stroke-width: 0px;white-space: normal;background-color: rgb(255, 255, 255);text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;\"><strong style=\"border: 0px;font-family: inherit;font-size: 16px;font-style: inherit;font-weight: bold;margin: 0px;outline: 0px;padding: 0px;vertical-align: baseline;overflow-wrap: break-word;box-sizing: border-box;\"><span leaf=\"\">微服务数据共享设计</span></strong></p><p data-track=\"16\" style=\"border: 0px;font-family: -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Microsoft YaHei&quot;, &quot;Source Han Sans SC&quot;, &quot;Noto Sans CJK SC&quot;, &quot;WenQuanYi Micro Hei&quot;, sans-serif;font-size: 16px;font-style: normal;font-weight: 400;margin: 0px 0px 1.7em;outline: 0px;padding: 0px;vertical-align: baseline;overflow-wrap: break-word;box-sizing: border-box;word-break: normal;line-height: 1.5;color: rgb(34, 34, 34);font-variant-ligatures: normal;font-variant-caps: normal;letter-spacing: normal;orphans: 2;text-align: start;text-indent: 0px;text-transform: none;widows: 2;word-spacing: 0px;-webkit-text-stroke-width: 0px;white-space: normal;background-color: rgb(255, 255, 255);text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;\"><span leaf=\"\">在微服务架构中，每个服务通常拥有独立的数据存储，比如；用户、商品…等等，这是为了保证服务的独立性和自治性。</span></p><p data-track=\"16\" style=\"border: 0px;font-family: -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Microsoft YaHei&quot;, &quot;Source Han Sans SC&quot;, &quot;Noto Sans CJK SC&quot;, &quot;WenQuanYi Micro Hei&quot;, sans-serif;font-size: 16px;font-style: normal;font-weight: 400;margin: 0px 0px 1.7em;outline: 0px;padding: 0px;vertical-align: baseline;overflow-wrap: break-word;box-sizing: border-box;word-break: normal;line-height: 1.5;color: rgb(34, 34, 34);font-variant-ligatures: normal;font-variant-caps: normal;letter-spacing: normal;orphans: 2;text-align: start;text-indent: 0px;text-transform: none;widows: 2;word-spacing: 0px;-webkit-text-stroke-width: 0px;white-space: normal;background-color: rgb(255, 255, 255);text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;\"><span leaf=\"\">然而，实际业务逻辑往往需要跨服务获取、或修改数据，或者只是“临时过渡”。</span></p><p data-track=\"16\" style=\"border: 0px;font-family: -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Microsoft YaHei&quot;, &quot;Source Han Sans SC&quot;, &quot;Noto Sans CJK SC&quot;, &quot;WenQuanYi Micro Hei&quot;, sans-serif;font-size: 16px;font-style: normal;font-weight: 400;margin: 0px 0px 1.7em;outline: 0px;padding: 0px;vertical-align: baseline;overflow-wrap: break-word;box-sizing: border-box;word-break: normal;line-height: 1.5;color: rgb(34, 34, 34);font-variant-ligatures: normal;font-variant-caps: normal;letter-spacing: normal;orphans: 2;text-align: start;text-indent: 0px;text-transform: none;widows: 2;word-spacing: 0px;-webkit-text-stroke-width: 0px;white-space: normal;background-color: rgb(255, 255, 255);text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;\"><span leaf=\"\">这就会涉及到“微服务数据共享设计”，可以理解为“反模式”。</span></p><section style=\"border: 0px;font-family: -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Microsoft YaHei&quot;, &quot;Source Han Sans SC&quot;, &quot;Noto Sans CJK SC&quot;, &quot;WenQuanYi Micro Hei&quot;, sans-serif;font-size: 16px;font-style: normal;font-weight: 400;margin: 0px 0px 1.7em;outline: 0px;padding: 0px;vertical-align: baseline;overflow-wrap: break-word;box-sizing: border-box;word-break: normal;line-height: 1.5;color: rgb(34, 34, 34);font-variant-ligatures: normal;font-variant-caps: normal;letter-spacing: normal;orphans: 2;text-align: start;text-indent: 0px;text-transform: none;widows: 2;word-spacing: 0px;-webkit-text-stroke-width: 0px;white-space: normal;background-color: rgb(255, 255, 255);text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;\" nodeleaf=\"\"><img data-src=\"https://mmbiz.qpic.cn/sz_mmbiz_png/WH9ojuibmKSp8kECvD1hgxrEspd1HNSQB3UicuO9AtxvNJhWANmQ6Smgdj0EpAicxjXYkBXMNSbDEA6iaFMetgnIdg/640?wx_fmt=png&amp;from=appmsg\" alt=\"最新文章\" class=\"rich_pages wxw-img\" data-ratio=\"0.6170212765957447\" data-type=\"png\" data-w=\"846\" height=\"363\" style=\"max-width: 100%;height: auto;object-fit: cover;border: 0px;vertical-align: text-top;opacity: 1;transition: opacity 400ms;cursor: zoom-in;\" width=\"588\" data-imgfileid=\"100006552\"></section><p style=\"border: 0px;font-family: -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Microsoft YaHei&quot;, &quot;Source Han Sans SC&quot;, &quot;Noto Sans CJK SC&quot;, &quot;WenQuanYi Micro Hei&quot;, sans-serif;font-size: 16px;font-style: normal;font-weight: 400;margin: 0px 0px 1.7em;outline: 0px;padding: 0px;vertical-align: baseline;overflow-wrap: break-word;box-sizing: border-box;word-break: normal;line-height: 1.5;color: rgb(34, 34, 34);font-variant-ligatures: normal;font-variant-caps: normal;letter-spacing: normal;orphans: 2;text-align: start;text-indent: 0px;text-transform: none;widows: 2;word-spacing: 0px;-webkit-text-stroke-width: 0px;white-space: normal;background-color: rgb(255, 255, 255);text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;\"><span leaf=\"\">&nbsp;</span></p><p style=\"border: 0px;font-family: -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Microsoft YaHei&quot;, &quot;Source Han Sans SC&quot;, &quot;Noto Sans CJK SC&quot;, &quot;WenQuanYi Micro Hei&quot;, sans-serif;font-size: 16px;font-style: normal;font-weight: 400;margin: 0px 0px 1.7em;outline: 0px;padding: 0px;vertical-align: baseline;overflow-wrap: break-word;box-sizing: border-box;word-break: normal;line-height: 1.5;color: rgb(34, 34, 34);font-variant-ligatures: normal;font-variant-caps: normal;letter-spacing: normal;orphans: 2;text-align: start;text-indent: 0px;text-transform: none;widows: 2;word-spacing: 0px;-webkit-text-stroke-width: 0px;white-space: normal;background-color: rgb(255, 255, 255);text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;\"><span leaf=\"\">作为反模式，通常不推荐。</span></p><p style=\"border: 0px;font-family: -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Microsoft YaHei&quot;, &quot;Source Han Sans SC&quot;, &quot;Noto Sans CJK SC&quot;, &quot;WenQuanYi Micro Hei&quot;, sans-serif;font-size: 16px;font-style: normal;font-weight: 400;margin: 0px 0px 1.7em;outline: 0px;padding: 0px;vertical-align: baseline;overflow-wrap: break-word;box-sizing: border-box;word-break: normal;line-height: 1.5;color: rgb(34, 34, 34);font-variant-ligatures: normal;font-variant-caps: normal;letter-spacing: normal;orphans: 2;text-align: start;text-indent: 0px;text-transform: none;widows: 2;word-spacing: 0px;-webkit-text-stroke-width: 0px;white-space: normal;background-color: rgb(255, 255, 255);text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;\"><span leaf=\"\">但是，在一些遗留系统向微服务改造的初期。</span></p><p style=\"border: 0px;font-family: -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Microsoft YaHei&quot;, &quot;Source Han Sans SC&quot;, &quot;Noto Sans CJK SC&quot;, &quot;WenQuanYi Micro Hei&quot;, sans-serif;font-size: 16px;font-style: normal;font-weight: 400;margin: 0px 0px 1.7em;outline: 0px;padding: 0px;vertical-align: baseline;overflow-wrap: break-word;box-sizing: border-box;word-break: normal;line-height: 1.5;color: rgb(34, 34, 34);font-variant-ligatures: normal;font-variant-caps: normal;letter-spacing: normal;orphans: 2;text-align: start;text-indent: 0px;text-transform: none;widows: 2;word-spacing: 0px;-webkit-text-stroke-width: 0px;white-space: normal;background-color: rgb(255, 255, 255);text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;\"><span leaf=\"\">为了快速解耦业务逻辑，而暂时保留数据库共享，但最终目标是拆分数据库。</span></p><p style=\"border: 0px;font-family: -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Microsoft YaHei&quot;, &quot;Source Han Sans SC&quot;, &quot;Noto Sans CJK SC&quot;, &quot;WenQuanYi Micro Hei&quot;, sans-serif;font-size: 16px;font-style: normal;font-weight: 400;margin: 0px 0px 1.7em;outline: 0px;padding: 0px;vertical-align: baseline;overflow-wrap: break-word;box-sizing: border-box;word-break: normal;line-height: 1.5;color: rgb(34, 34, 34);font-variant-ligatures: normal;font-variant-caps: normal;letter-spacing: normal;orphans: 2;text-align: start;text-indent: 0px;text-transform: none;widows: 2;word-spacing: 0px;-webkit-text-stroke-width: 0px;white-space: normal;background-color: rgb(255, 255, 255);text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;\"><span leaf=\"\">&nbsp;</span></p><p data-track=\"27\" style=\"border: 0px;font-family: -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Microsoft YaHei&quot;, &quot;Source Han Sans SC&quot;, &quot;Noto Sans CJK SC&quot;, &quot;WenQuanYi Micro Hei&quot;, sans-serif;font-size: 16px;font-style: normal;font-weight: 400;margin: 0px 0px 1.7em;outline: 0px;padding: 0px;vertical-align: baseline;overflow-wrap: break-word;box-sizing: border-box;word-break: normal;line-height: 1.5;color: rgb(34, 34, 34);font-variant-ligatures: normal;font-variant-caps: normal;letter-spacing: normal;orphans: 2;text-align: start;text-indent: 0px;text-transform: none;widows: 2;word-spacing: 0px;-webkit-text-stroke-width: 0px;white-space: normal;background-color: rgb(255, 255, 255);text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;\"><strong style=\"border: 0px;font-family: inherit;font-size: 16px;font-style: inherit;font-weight: bold;margin: 0px;outline: 0px;padding: 0px;vertical-align: baseline;overflow-wrap: break-word;box-sizing: border-box;\"><span leaf=\"\">微服务聚合设计</span></strong></p><p data-start=\"130\" data-end=\"143\" style=\"border: 0px;font-family: -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Microsoft YaHei&quot;, &quot;Source Han Sans SC&quot;, &quot;Noto Sans CJK SC&quot;, &quot;WenQuanYi Micro Hei&quot;, sans-serif;font-size: 16px;font-style: normal;font-weight: 400;margin: 0px 0px 1.7em;outline: 0px;padding: 0px;vertical-align: baseline;overflow-wrap: break-word;box-sizing: border-box;word-break: normal;line-height: 1.5;color: rgb(34, 34, 34);font-variant-ligatures: normal;font-variant-caps: normal;letter-spacing: normal;orphans: 2;text-align: start;text-indent: 0px;text-transform: none;widows: 2;word-spacing: 0px;-webkit-text-stroke-width: 0px;white-space: normal;background-color: rgb(255, 255, 255);text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;\"><span leaf=\"\">聚合设计模式，核心思想是:“一个请求，多个服务，聚合响应，一并返回。”</span></p><p data-start=\"175\" data-end=\"197\" style=\"border: 0px;font-family: -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Microsoft YaHei&quot;, &quot;Source Han Sans SC&quot;, &quot;Noto Sans CJK SC&quot;, &quot;WenQuanYi Micro Hei&quot;, sans-serif;font-size: 16px;font-style: normal;font-weight: 400;margin: 0px 0px 1.7em;outline: 0px;padding: 0px;vertical-align: baseline;overflow-wrap: break-word;box-sizing: border-box;word-break: normal;line-height: 1.5;color: rgb(34, 34, 34);font-variant-ligatures: normal;font-variant-caps: normal;letter-spacing: normal;orphans: 2;text-align: start;text-indent: 0px;text-transform: none;widows: 2;word-spacing: 0px;-webkit-text-stroke-width: 0px;white-space: normal;background-color: rgb(255, 255, 255);text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;\"><span leaf=\"\">例如：在一个电商订单详情页中，前端需要展示：</span></p><ul style=\"border: 0px;font-family: -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Microsoft YaHei&quot;, &quot;Source Han Sans SC&quot;, &quot;Noto Sans CJK SC&quot;, &quot;WenQuanYi Micro Hei&quot;, sans-serif;font-size: 16px;font-style: normal;font-weight: 400;margin: 0px 0px 1.2em 2em;outline: 0px;padding: 0px;vertical-align: baseline;overflow-wrap: break-word;box-sizing: border-box;list-style: disc;line-height: 1.8;color: rgb(34, 34, 34);font-variant-ligatures: normal;font-variant-caps: normal;letter-spacing: normal;orphans: 2;text-align: start;text-indent: 0px;text-transform: none;widows: 2;word-spacing: 0px;-webkit-text-stroke-width: 0px;white-space: normal;background-color: rgb(255, 255, 255);text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;\" class=\"list-paddingleft-1\"><li style=\"border: 0px;font-family: inherit;font-size: 16px;font-style: inherit;font-weight: inherit;margin: 0px;outline: 0px;padding: 0px;vertical-align: baseline;overflow-wrap: break-word;box-sizing: border-box;\"><p data-start=\"200\" data-end=\"212\" style=\"border: 0px;font-family: inherit;font-size: 16px;font-style: inherit;font-weight: inherit;margin: 0px 0px 1.7em;outline: 0px;padding: 0px;vertical-align: baseline;overflow-wrap: break-word;box-sizing: border-box;\"><span leaf=\"\">订单基本信息（订单服务）；</span></p></li><li style=\"border: 0px;font-family: inherit;font-size: 16px;font-style: inherit;font-weight: inherit;margin: 5px 0px 0px;outline: 0px;padding: 0px;vertical-align: baseline;overflow-wrap: break-word;box-sizing: border-box;\"><p data-start=\"215\" data-end=\"225\" style=\"border: 0px;font-family: inherit;font-size: 16px;font-style: inherit;font-weight: inherit;margin: 0px 0px 1.7em;outline: 0px;padding: 0px;vertical-align: baseline;overflow-wrap: break-word;box-sizing: border-box;\"><span leaf=\"\">商品详情（商品服务）；</span></p></li><li style=\"border: 0px;font-family: inherit;font-size: 16px;font-style: inherit;font-weight: inherit;margin: 5px 0px 0px;outline: 0px;padding: 0px;vertical-align: baseline;overflow-wrap: break-word;box-sizing: border-box;\"><p data-start=\"228\" data-end=\"238\" style=\"border: 0px;font-family: inherit;font-size: 16px;font-style: inherit;font-weight: inherit;margin: 0px 0px 1.7em;outline: 0px;padding: 0px;vertical-align: baseline;overflow-wrap: break-word;box-sizing: border-box;\"><span leaf=\"\">收货地址（用户服务）；</span></p></li><li style=\"border: 0px;font-family: inherit;font-size: 16px;font-style: inherit;font-weight: inherit;margin: 5px 0px 0px;outline: 0px;padding: 0px;vertical-align: baseline;overflow-wrap: break-word;box-sizing: border-box;\"><p data-start=\"241\" data-end=\"251\" style=\"border: 0px;font-family: inherit;font-size: 16px;font-style: inherit;font-weight: inherit;margin: 0px 0px 1.7em;outline: 0px;padding: 0px;vertical-align: baseline;overflow-wrap: break-word;box-sizing: border-box;\"><span leaf=\"\">配送进度（物流服务）；</span></p></li></ul><p data-start=\"253\" data-end=\"298\" style=\"border: 0px;font-family: -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Microsoft YaHei&quot;, &quot;Source Han Sans SC&quot;, &quot;Noto Sans CJK SC&quot;, &quot;WenQuanYi Micro Hei&quot;, sans-serif;font-size: 16px;font-style: normal;font-weight: 400;margin: 0px 0px 1.7em;outline: 0px;padding: 0px;vertical-align: baseline;overflow-wrap: break-word;box-sizing: border-box;word-break: normal;line-height: 1.5;color: rgb(34, 34, 34);font-variant-ligatures: normal;font-variant-caps: normal;letter-spacing: normal;orphans: 2;text-align: start;text-indent: 0px;text-transform: none;widows: 2;word-spacing: 0px;-webkit-text-stroke-width: 0px;white-space: normal;background-color: rgb(255, 255, 255);text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;\"><span leaf=\"\">如果每个服务都需要单独调用并组合，前端或调用方将承担很大的聚合负担，带来性能和稳定性问题。</span></p><p data-start=\"300\" data-end=\"349\" style=\"border: 0px;font-family: -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Microsoft YaHei&quot;, &quot;Source Han Sans SC&quot;, &quot;Noto Sans CJK SC&quot;, &quot;WenQuanYi Micro Hei&quot;, sans-serif;font-size: 16px;font-style: normal;font-weight: 400;margin: 0px 0px 1.7em;outline: 0px;padding: 0px;vertical-align: baseline;overflow-wrap: break-word;box-sizing: border-box;word-break: normal;line-height: 1.5;color: rgb(34, 34, 34);font-variant-ligatures: normal;font-variant-caps: normal;letter-spacing: normal;orphans: 2;text-align: start;text-indent: 0px;text-transform: none;widows: 2;word-spacing: 0px;-webkit-text-stroke-width: 0px;white-space: normal;background-color: rgb(255, 255, 255);text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;\"><span leaf=\"\">聚合模式的目标，由一个中间层组件来统一发起服务请求，整合结果，并返回一个完整响应给客户端。</span></p><p data-start=\"300\" data-end=\"349\" style=\"border: 0px;font-family: -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Microsoft YaHei&quot;, &quot;Source Han Sans SC&quot;, &quot;Noto Sans CJK SC&quot;, &quot;WenQuanYi Micro Hei&quot;, sans-serif;font-size: 16px;font-style: normal;font-weight: 400;margin: 0px 0px 1.7em;outline: 0px;padding: 0px;vertical-align: baseline;overflow-wrap: break-word;box-sizing: border-box;word-break: normal;line-height: 1.5;color: rgb(34, 34, 34);font-variant-ligatures: normal;font-variant-caps: normal;letter-spacing: normal;orphans: 2;text-align: start;text-indent: 0px;text-transform: none;widows: 2;word-spacing: 0px;-webkit-text-stroke-width: 0px;white-space: normal;background-color: rgb(255, 255, 255);text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;\"><span leaf=\"\">如下图所示：</span></p><section data-start=\"300\" data-end=\"349\" style=\"border: 0px;font-family: -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Microsoft YaHei&quot;, &quot;Source Han Sans SC&quot;, &quot;Noto Sans CJK SC&quot;, &quot;WenQuanYi Micro Hei&quot;, sans-serif;font-size: 16px;font-style: normal;font-weight: 400;margin: 0px 0px 1.7em;outline: 0px;padding: 0px;vertical-align: baseline;overflow-wrap: break-word;box-sizing: border-box;word-break: normal;line-height: 1.5;color: rgb(34, 34, 34);font-variant-ligatures: normal;font-variant-caps: normal;letter-spacing: normal;orphans: 2;text-align: start;text-indent: 0px;text-transform: none;widows: 2;word-spacing: 0px;-webkit-text-stroke-width: 0px;white-space: normal;background-color: rgb(255, 255, 255);text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;\" nodeleaf=\"\"><img data-src=\"https://mmbiz.qpic.cn/sz_mmbiz_png/WH9ojuibmKSp8kECvD1hgxrEspd1HNSQB4VWsRuYicpe8quAQl5rjHV7YVnl8ZoTW5z4NaE6CBWtLSBZfhhD47Bw/640?wx_fmt=png&amp;from=appmsg\" alt=\"最新文章\" class=\"rich_pages wxw-img\" data-ratio=\"0.5177033492822967\" data-type=\"png\" data-w=\"1045\" height=\"340\" style=\"max-width: 100%;height: auto;object-fit: cover;border: 0px;vertical-align: text-top;opacity: 1;transition: opacity 400ms;cursor: zoom-in;\" width=\"657\" data-imgfileid=\"100006558\"></section><pre data-enlighter-language=\"generic\" style=\"border: 0px !important;font: 400 0.9375rem / 1.6 &quot;Courier 10 Pitch&quot;, Courier, monospace;margin: 0px 0px 1.7em;outline: 0px;padding: 1.5em 1.5em 1.5em 50px;vertical-align: baseline;overflow-wrap: break-word;box-sizing: border-box;background: rgb(47, 54, 64);white-space: revert;border-radius: 3px;max-width: 100%;overflow: auto hidden;width: 908px;color: rgb(34, 34, 34);letter-spacing: normal;orphans: 2;text-align: start;text-indent: 0px;text-transform: none;widows: 2;word-spacing: 0px;-webkit-text-stroke-width: 0px;text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;\"><ol style=\"border: 0px;font-family: inherit;font-size: 15px;font-style: inherit;font-weight: inherit;margin: 0px;outline: 0px;padding: 0px;vertical-align: baseline;overflow-wrap: break-word;box-sizing: border-box;list-style: none;color: rgb(101, 109, 120);\" class=\"list-paddingleft-1\"><li style=\"border: 0px;font-family: inherit;font-size: 15px;font-style: inherit;font-weight: inherit;margin: 0px !important;outline: 0px;padding: 0px 0px 0px 0.5em;vertical-align: baseline;overflow-wrap: break-word;box-sizing: border-box;background-color: rgb(47, 54, 64);list-style-type: decimal;\"><span style=\"border: 0px;font-family: inherit;font-size: 15px;font-style: inherit;font-weight: inherit;margin: 0px;outline: 0px;padding: 0px;vertical-align: baseline;overflow-wrap: break-word;box-sizing: border-box;color: rgb(230, 233, 237);\"><span leaf=\"\">【客户端请求】</span></span></li><li style=\"border: 0px;font-family: inherit;font-size: 15px;font-style: inherit;font-weight: inherit;margin: 0px !important;outline: 0px;padding: 0px 0px 0px 0.5em;vertical-align: baseline;overflow-wrap: break-word;box-sizing: border-box;background-color: rgb(47, 54, 64);list-style-type: decimal;\"><span style=\"border: 0px;font-family: inherit;font-size: 15px;font-style: inherit;font-weight: inherit;margin: 0px;outline: 0px;padding: 0px;vertical-align: baseline;overflow-wrap: break-word;box-sizing: border-box;color: rgb(230, 233, 237);\"></span><span style=\"border: 0px;font-family: inherit;font-size: 15px;font-style: inherit;font-weight: inherit;margin: 0px;outline: 0px;padding: 0px;vertical-align: baseline;overflow-wrap: break-word;box-sizing: border-box;color: rgb(230, 233, 237);\"><span leaf=\"\">↓</span></span></li><li style=\"border: 0px;font-family: inherit;font-size: 15px;font-style: inherit;font-weight: inherit;margin: 0px !important;outline: 0px;padding: 0px 0px 0px 0.5em;vertical-align: baseline;overflow-wrap: break-word;box-sizing: border-box;background-color: rgb(47, 54, 64);list-style-type: decimal;\"><span style=\"border: 0px;font-family: inherit;font-size: 15px;font-style: inherit;font-weight: inherit;margin: 0px;outline: 0px;padding: 0px;vertical-align: baseline;overflow-wrap: break-word;box-sizing: border-box;color: rgb(230, 233, 237);\"><span leaf=\"\">【聚合服务或</span></span><span style=\"border: 0px;font-family: inherit;font-size: 15px;font-style: inherit;font-weight: inherit;margin: 0px;outline: 0px;padding: 0px;vertical-align: baseline;overflow-wrap: break-word;box-sizing: border-box;color: rgb(230, 233, 237);\"><span leaf=\"\">&nbsp;API&nbsp;</span></span><span style=\"border: 0px;font-family: inherit;font-size: 15px;font-style: inherit;font-weight: inherit;margin: 0px;outline: 0px;padding: 0px;vertical-align: baseline;overflow-wrap: break-word;box-sizing: border-box;color: rgb(230, 233, 237);\"><span leaf=\"\">网关】</span></span></li><li style=\"border: 0px;font-family: inherit;font-size: 15px;font-style: inherit;font-weight: inherit;margin: 0px !important;outline: 0px;padding: 0px 0px 0px 0.5em;vertical-align: baseline;overflow-wrap: break-word;box-sizing: border-box;background-color: rgb(47, 54, 64);list-style-type: decimal;\"><span style=\"border: 0px;font-family: inherit;font-size: 15px;font-style: inherit;font-weight: inherit;margin: 0px;outline: 0px;padding: 0px;vertical-align: baseline;overflow-wrap: break-word;box-sizing: border-box;color: rgb(230, 233, 237);\"></span><span style=\"border: 0px;font-family: inherit;font-size: 15px;font-style: inherit;font-weight: inherit;margin: 0px;outline: 0px;padding: 0px;vertical-align: baseline;overflow-wrap: break-word;box-sizing: border-box;color: rgb(230, 233, 237);\"><span leaf=\"\">↓</span></span></li><li style=\"border: 0px;font-family: inherit;font-size: 15px;font-style: inherit;font-weight: inherit;margin: 0px !important;outline: 0px;padding: 0px 0px 0px 0.5em;vertical-align: baseline;overflow-wrap: break-word;box-sizing: border-box;background-color: rgb(47, 54, 64);list-style-type: decimal;\"><span style=\"border: 0px;font-family: inherit;font-size: 15px;font-style: inherit;font-weight: inherit;margin: 0px;outline: 0px;padding: 0px;vertical-align: baseline;overflow-wrap: break-word;box-sizing: border-box;color: rgb(230, 233, 237);\"><span leaf=\"\">┌──────────────┬──────────────┬──────────────┐</span></span></li><li style=\"border: 0px;font-family: inherit;font-size: 15px;font-style: inherit;font-weight: inherit;margin: 0px !important;outline: 0px;padding: 0px 0px 0px 0.5em;vertical-align: baseline;overflow-wrap: break-word;box-sizing: border-box;background-color: rgb(47, 54, 64);list-style-type: decimal;\"><span style=\"border: 0px;font-family: inherit;font-size: 15px;font-style: inherit;font-weight: inherit;margin: 0px;outline: 0px;padding: 0px;vertical-align: baseline;overflow-wrap: break-word;box-sizing: border-box;color: rgb(230, 233, 237);\"><span leaf=\"\">│调用订单服务</span></span><span style=\"border: 0px;font-family: inherit;font-size: 15px;font-style: inherit;font-weight: inherit;margin: 0px;outline: 0px;padding: 0px;vertical-align: baseline;overflow-wrap: break-word;box-sizing: border-box;color: rgb(230, 233, 237);\"></span><span style=\"border: 0px;font-family: inherit;font-size: 15px;font-style: inherit;font-weight: inherit;margin: 0px;outline: 0px;padding: 0px;vertical-align: baseline;overflow-wrap: break-word;box-sizing: border-box;color: rgb(230, 233, 237);\"><span leaf=\"\">│调用商品服务</span></span><span style=\"border: 0px;font-family: inherit;font-size: 15px;font-style: inherit;font-weight: inherit;margin: 0px;outline: 0px;padding: 0px;vertical-align: baseline;overflow-wrap: break-word;box-sizing: border-box;color: rgb(230, 233, 237);\"></span><span style=\"border: 0px;font-family: inherit;font-size: 15px;font-style: inherit;font-weight: inherit;margin: 0px;outline: 0px;padding: 0px;vertical-align: baseline;overflow-wrap: break-word;box-sizing: border-box;color: rgb(230, 233, 237);\"><span leaf=\"\">│调用用户服务</span></span><span style=\"border: 0px;font-family: inherit;font-size: 15px;font-style: inherit;font-weight: inherit;margin: 0px;outline: 0px;padding: 0px;vertical-align: baseline;overflow-wrap: break-word;box-sizing: border-box;color: rgb(230, 233, 237);\"></span><span style=\"border: 0px;font-family: inherit;font-size: 15px;font-style: inherit;font-weight: inherit;margin: 0px;outline: 0px;padding: 0px;vertical-align: baseline;overflow-wrap: break-word;box-sizing: border-box;color: rgb(230, 233, 237);\"><span leaf=\"\">│</span></span></li><li style=\"border: 0px;font-family: inherit;font-size: 15px;font-style: inherit;font-weight: inherit;margin: 0px !important;outline: 0px;padding: 0px 0px 0px 0.5em;vertical-align: baseline;overflow-wrap: break-word;box-sizing: border-box;background-color: rgb(47, 54, 64);list-style-type: decimal;\"><span style=\"border: 0px;font-family: inherit;font-size: 15px;font-style: inherit;font-weight: inherit;margin: 0px;outline: 0px;padding: 0px;vertical-align: baseline;overflow-wrap: break-word;box-sizing: border-box;color: rgb(230, 233, 237);\"><span leaf=\"\">└──────────────┴──────────────┴──────────────┘</span></span></li><li style=\"border: 0px;font-family: inherit;font-size: 15px;font-style: inherit;font-weight: inherit;margin: 0px !important;outline: 0px;padding: 0px 0px 0px 0.5em;vertical-align: baseline;overflow-wrap: break-word;box-sizing: border-box;background-color: rgb(47, 54, 64);list-style-type: decimal;\"><span style=\"border: 0px;font-family: inherit;font-size: 15px;font-style: inherit;font-weight: inherit;margin: 0px;outline: 0px;padding: 0px;vertical-align: baseline;overflow-wrap: break-word;box-sizing: border-box;color: rgb(230, 233, 237);\"></span><span style=\"border: 0px;font-family: inherit;font-size: 15px;font-style: inherit;font-weight: inherit;margin: 0px;outline: 0px;padding: 0px;vertical-align: baseline;overflow-wrap: break-word;box-sizing: border-box;color: rgb(230, 233, 237);\"><span leaf=\"\">↓</span></span></li><li style=\"border: 0px;font-family: inherit;font-size: 15px;font-style: inherit;font-weight: inherit;margin: 0px !important;outline: 0px;padding: 0px 0px 0px 0.5em;vertical-align: baseline;overflow-wrap: break-word;box-sizing: border-box;background-color: rgb(47, 54, 64);list-style-type: decimal;\"><span style=\"border: 0px;font-family: inherit;font-size: 15px;font-style: inherit;font-weight: inherit;margin: 0px;outline: 0px;padding: 0px;vertical-align: baseline;overflow-wrap: break-word;box-sizing: border-box;color: rgb(230, 233, 237);\"><span leaf=\"\">【整合响应结果】</span></span></li><li style=\"border: 0px;font-family: inherit;font-size: 15px;font-style: inherit;font-weight: inherit;margin: 0px !important;outline: 0px;padding: 0px 0px 0px 0.5em;vertical-align: baseline;overflow-wrap: break-word;box-sizing: border-box;background-color: rgb(47, 54, 64);list-style-type: decimal;\"><span style=\"border: 0px;font-family: inherit;font-size: 15px;font-style: inherit;font-weight: inherit;margin: 0px;outline: 0px;padding: 0px;vertical-align: baseline;overflow-wrap: break-word;box-sizing: border-box;color: rgb(230, 233, 237);\"></span><span style=\"border: 0px;font-family: inherit;font-size: 15px;font-style: inherit;font-weight: inherit;margin: 0px;outline: 0px;padding: 0px;vertical-align: baseline;overflow-wrap: break-word;box-sizing: border-box;color: rgb(230, 233, 237);\"><span leaf=\"\">↓</span></span></li><li style=\"border: 0px;font-family: inherit;font-size: 15px;font-style: inherit;font-weight: inherit;margin: 0px !important;outline: 0px;padding: 0px 0px 0px 0.5em;vertical-align: baseline;overflow-wrap: break-word;box-sizing: border-box;background-color: rgb(47, 54, 64);list-style-type: decimal;\"><span style=\"border: 0px;font-family: inherit;font-size: 15px;font-style: inherit;font-weight: inherit;margin: 0px;outline: 0px;padding: 0px;vertical-align: baseline;overflow-wrap: break-word;box-sizing: border-box;color: rgb(230, 233, 237);\"><span leaf=\"\">【返回统一响应给客户端】</span></span></li></ol></pre><p data-track=\"27\" style=\"border: 0px;font-family: -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Microsoft YaHei&quot;, &quot;Source Han Sans SC&quot;, &quot;Noto Sans CJK SC&quot;, &quot;WenQuanYi Micro Hei&quot;, sans-serif;font-size: 16px;font-style: normal;font-weight: 400;margin: 0px 0px 1.7em;outline: 0px;padding: 0px;vertical-align: baseline;overflow-wrap: break-word;box-sizing: border-box;word-break: normal;line-height: 1.5;color: rgb(34, 34, 34);font-variant-ligatures: normal;font-variant-caps: normal;letter-spacing: normal;orphans: 2;text-align: start;text-indent: 0px;text-transform: none;widows: 2;word-spacing: 0px;-webkit-text-stroke-width: 0px;white-space: normal;background-color: rgb(255, 255, 255);text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;\"><span leaf=\"\">微服务聚合设计模式，将这些分散的服务调用聚合起来。</span></p><p data-track=\"27\" style=\"border: 0px;font-family: -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Microsoft YaHei&quot;, &quot;Source Han Sans SC&quot;, &quot;Noto Sans CJK SC&quot;, &quot;WenQuanYi Micro Hei&quot;, sans-serif;font-size: 16px;font-style: normal;font-weight: 400;margin: 0px 0px 1.7em;outline: 0px;padding: 0px;vertical-align: baseline;overflow-wrap: break-word;box-sizing: border-box;word-break: normal;line-height: 1.5;color: rgb(34, 34, 34);font-variant-ligatures: normal;font-variant-caps: normal;letter-spacing: normal;orphans: 2;text-align: start;text-indent: 0px;text-transform: none;widows: 2;word-spacing: 0px;-webkit-text-stroke-width: 0px;white-space: normal;background-color: rgb(255, 255, 255);text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;\"><span leaf=\"\">提供一个统一的、为特定客户端定制的接口，避免客户端直接进行复杂的扇出调用和数据组合。</span></p><p data-track=\"27\" style=\"border: 0px;font-family: -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Microsoft YaHei&quot;, &quot;Source Han Sans SC&quot;, &quot;Noto Sans CJK SC&quot;, &quot;WenQuanYi Micro Hei&quot;, sans-serif;font-size: 16px;font-style: normal;font-weight: 400;margin: 0px 0px 1.7em;outline: 0px;padding: 0px;vertical-align: baseline;overflow-wrap: break-word;box-sizing: border-box;word-break: normal;line-height: 1.5;color: rgb(34, 34, 34);font-variant-ligatures: normal;font-variant-caps: normal;letter-spacing: normal;orphans: 2;text-align: start;text-indent: 0px;text-transform: none;widows: 2;word-spacing: 0px;-webkit-text-stroke-width: 0px;white-space: normal;background-color: rgb(255, 255, 255);text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;\"><span leaf=\"\"><br></span></p><p data-track=\"4\" style=\"border: 0px;font-family: -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Microsoft YaHei&quot;, &quot;Source Han Sans SC&quot;, &quot;Noto Sans CJK SC&quot;, &quot;WenQuanYi Micro Hei&quot;, sans-serif;font-size: 16px;font-style: normal;font-weight: 400;margin: 0px 0px 1.7em;outline: 0px;padding: 0px;vertical-align: baseline;overflow-wrap: break-word;box-sizing: border-box;word-break: normal;line-height: 1.5;color: rgb(34, 34, 34);font-variant-ligatures: normal;font-variant-caps: normal;letter-spacing: normal;orphans: 2;text-align: start;text-indent: 0px;text-transform: none;widows: 2;word-spacing: 0px;-webkit-text-stroke-width: 0px;white-space: normal;background-color: rgb(255, 255, 255);text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;\"><strong style=\"border: 0px;font-family: inherit;font-size: 16px;font-style: inherit;font-weight: bold;margin: 0px;outline: 0px;padding: 0px;vertical-align: baseline;overflow-wrap: break-word;box-sizing: border-box;\"><span leaf=\"\">微服务代理设计</span></strong></p><p data-track=\"4\" style=\"border: 0px;font-family: -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Microsoft YaHei&quot;, &quot;Source Han Sans SC&quot;, &quot;Noto Sans CJK SC&quot;, &quot;WenQuanYi Micro Hei&quot;, sans-serif;font-size: 16px;font-style: normal;font-weight: 400;margin: 0px 0px 1.7em;outline: 0px;padding: 0px;vertical-align: baseline;overflow-wrap: break-word;box-sizing: border-box;word-break: normal;line-height: 1.5;color: rgb(34, 34, 34);font-variant-ligatures: normal;font-variant-caps: normal;letter-spacing: normal;orphans: 2;text-align: start;text-indent: 0px;text-transform: none;widows: 2;word-spacing: 0px;-webkit-text-stroke-width: 0px;white-space: normal;background-color: rgb(255, 255, 255);text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;\"><span leaf=\"\">微服务代理模式，是在微服务架构中扮演着“中间人”的角色。</span></p><p data-track=\"4\" style=\"border: 0px;font-family: -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Microsoft YaHei&quot;, &quot;Source Han Sans SC&quot;, &quot;Noto Sans CJK SC&quot;, &quot;WenQuanYi Micro Hei&quot;, sans-serif;font-size: 16px;font-style: normal;font-weight: 400;margin: 0px 0px 1.7em;outline: 0px;padding: 0px;vertical-align: baseline;overflow-wrap: break-word;box-sizing: border-box;word-break: normal;line-height: 1.5;color: rgb(34, 34, 34);font-variant-ligatures: normal;font-variant-caps: normal;letter-spacing: normal;orphans: 2;text-align: start;text-indent: 0px;text-transform: none;widows: 2;word-spacing: 0px;-webkit-text-stroke-width: 0px;white-space: normal;background-color: rgb(255, 255, 255);text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;\"><span leaf=\"\">它不直接处理业务逻辑，而是为服务间的通信提供额外的基础设施功能、或抽象层。</span></p><p data-track=\"4\" style=\"border: 0px;font-family: -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Microsoft YaHei&quot;, &quot;Source Han Sans SC&quot;, &quot;Noto Sans CJK SC&quot;, &quot;WenQuanYi Micro Hei&quot;, sans-serif;font-size: 16px;font-style: normal;font-weight: 400;margin: 0px 0px 1.7em;outline: 0px;padding: 0px;vertical-align: baseline;overflow-wrap: break-word;box-sizing: border-box;word-break: normal;line-height: 1.5;color: rgb(34, 34, 34);font-variant-ligatures: normal;font-variant-caps: normal;letter-spacing: normal;orphans: 2;text-align: start;text-indent: 0px;text-transform: none;widows: 2;word-spacing: 0px;-webkit-text-stroke-width: 0px;white-space: normal;background-color: rgb(255, 255, 255);text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;\"><span leaf=\"\">以增强服务间的访问控制、路由、安全、可观测性、韧性…等，同时尽可能地减少对业务代码的侵入。</span></p><section nodeleaf=\"\"><img data-src=\"https://mmbiz.qpic.cn/sz_mmbiz_png/WH9ojuibmKSp8kECvD1hgxrEspd1HNSQB48sW8tI9ibbHUhME3FvDT3gQ8jsh5nsL5Sz3icQUht7xymdMCDppE9QA/640?wx_fmt=png&amp;from=appmsg\" alt=\"最新文章\" class=\"rich_pages wxw-img\" data-ratio=\"0.5149023638232272\" data-type=\"png\" data-w=\"973\" height=\"336\" style=\"max-width: 100%;height: auto;object-fit: cover;border: 0px;vertical-align: text-top;opacity: 1;transition: opacity 400ms;cursor: zoom-in;\" width=\"653\" data-imgfileid=\"100006554\"></section><p><span leaf=\"\">比如：Sidecar 模式是一种在微服务架构中，广泛应用的代理设计模式。</span></p><section nodeleaf=\"\"><img data-src=\"https://mmbiz.qpic.cn/sz_mmbiz_png/WH9ojuibmKSp8kECvD1hgxrEspd1HNSQBzWh54ibe0jqiav0eZfx48P3z3h6iaV51Wdozgbh0Zoc5mjx9kocQCdcEA/640?wx_fmt=png&amp;from=appmsg\" alt=\"最新文章\" class=\"rich_pages wxw-img\" data-ratio=\"0.5580645161290323\" data-type=\"png\" data-w=\"620\" height=\"346\" style=\"max-width: 100%;height: auto;object-fit: cover;border: 0px;vertical-align: text-top;opacity: 1;transition: opacity 400ms;cursor: zoom-in;\" width=\"620\" data-imgfileid=\"100006555\"></section><pre data-enlighter-language=\"generic\" style=\"border: 0px !important;font: 0.9375rem / 1.6 &quot;Courier 10 Pitch&quot;, Courier, monospace;margin: 0px 0px 1.6em;outline: 0px;padding: 1.5em 1.5em 1.5em 50px;vertical-align: baseline;overflow-wrap: break-word;box-sizing: border-box;background: rgb(47, 54, 64);white-space: revert;border-radius: 3px;max-width: 100%;overflow: auto hidden;width: 908px;\"><ol style=\"border: 0px;font-family: inherit;font-size: 15px;font-style: inherit;font-weight: inherit;margin: 0px;outline: 0px;padding: 0px;vertical-align: baseline;overflow-wrap: break-word;box-sizing: border-box;list-style: none;color: rgb(101, 109, 120);\" class=\"list-paddingleft-1\"><li style=\"border: 0px;font-family: inherit;font-size: 15px;font-style: inherit;font-weight: inherit;margin: 0px;outline: 0px;padding: 0px 0px 0px 0.5em;vertical-align: baseline;overflow-wrap: break-word;box-sizing: border-box;background-color: rgb(47, 54, 64);list-style-type: decimal;\"><span style=\"border: 0px;font-family: inherit;font-size: 15px;font-style: inherit;font-weight: inherit;margin: 0px;outline: 0px;padding: 0px;vertical-align: baseline;overflow-wrap: break-word;box-sizing: border-box;color: rgb(230, 233, 237);\"><span leaf=\"\">┌─────────────────────┐</span></span></li><li style=\"border: 0px;font-family: inherit;font-size: 15px;font-style: inherit;font-weight: inherit;margin: 0px;outline: 0px;padding: 0px 0px 0px 0.5em;vertical-align: baseline;overflow-wrap: break-word;box-sizing: border-box;background-color: rgb(47, 54, 64);list-style-type: decimal;\"><span style=\"border: 0px;font-family: inherit;font-size: 15px;font-style: inherit;font-weight: inherit;margin: 0px;outline: 0px;padding: 0px;vertical-align: baseline;overflow-wrap: break-word;box-sizing: border-box;color: rgb(230, 233, 237);\"><span leaf=\"\">│</span></span><span style=\"border: 0px;font-family: inherit;font-size: 15px;font-style: inherit;font-weight: inherit;margin: 0px;outline: 0px;padding: 0px;vertical-align: baseline;overflow-wrap: break-word;box-sizing: border-box;color: rgb(230, 233, 237);\"></span><span style=\"border: 0px;font-family: inherit;font-size: 15px;font-style: inherit;font-weight: inherit;margin: 0px;outline: 0px;padding: 0px;vertical-align: baseline;overflow-wrap: break-word;box-sizing: border-box;color: rgb(230, 233, 237);\"><span leaf=\"\">应用服务容器</span></span><span style=\"border: 0px;font-family: inherit;font-size: 15px;font-style: inherit;font-weight: inherit;margin: 0px;outline: 0px;padding: 0px;vertical-align: baseline;overflow-wrap: break-word;box-sizing: border-box;color: rgb(230, 233, 237);\"><span leaf=\"\">&nbsp;A &nbsp; &nbsp;&nbsp;</span></span><span style=\"border: 0px;font-family: inherit;font-size: 15px;font-style: inherit;font-weight: inherit;margin: 0px;outline: 0px;padding: 0px;vertical-align: baseline;overflow-wrap: break-word;box-sizing: border-box;color: rgb(230, 233, 237);\"><span leaf=\"\">│</span></span></li><li style=\"border: 0px;font-family: inherit;font-size: 15px;font-style: inherit;font-weight: inherit;margin: 0px;outline: 0px;padding: 0px 0px 0px 0.5em;vertical-align: baseline;overflow-wrap: break-word;box-sizing: border-box;background-color: rgb(47, 54, 64);list-style-type: decimal;\"><span style=\"border: 0px;font-family: inherit;font-size: 15px;font-style: inherit;font-weight: inherit;margin: 0px;outline: 0px;padding: 0px;vertical-align: baseline;overflow-wrap: break-word;box-sizing: border-box;color: rgb(230, 233, 237);\"><span leaf=\"\">└────────┬────────────┘</span></span></li><li style=\"border: 0px;font-family: inherit;font-size: 15px;font-style: inherit;font-weight: inherit;margin: 0px;outline: 0px;padding: 0px 0px 0px 0.5em;vertical-align: baseline;overflow-wrap: break-word;box-sizing: border-box;background-color: rgb(47, 54, 64);list-style-type: decimal;\"><span style=\"border: 0px;font-family: inherit;font-size: 15px;font-style: inherit;font-weight: inherit;margin: 0px;outline: 0px;padding: 0px;vertical-align: baseline;overflow-wrap: break-word;box-sizing: border-box;color: rgb(230, 233, 237);\"></span><span style=\"border: 0px;font-family: inherit;font-size: 15px;font-style: inherit;font-weight: inherit;margin: 0px;outline: 0px;padding: 0px;vertical-align: baseline;overflow-wrap: break-word;box-sizing: border-box;color: rgb(230, 233, 237);\"><span leaf=\"\">│</span></span><span style=\"border: 0px;font-family: inherit;font-size: 15px;font-style: inherit;font-weight: inherit;margin: 0px;outline: 0px;padding: 0px;vertical-align: baseline;overflow-wrap: break-word;box-sizing: border-box;color: rgb(230, 233, 237);\"></span><span style=\"border: 0px;font-family: inherit;font-size: 15px;font-style: inherit;font-weight: inherit;margin: 0px;outline: 0px;padding: 0px;vertical-align: baseline;overflow-wrap: break-word;box-sizing: border-box;color: rgb(230, 233, 237);\"><span leaf=\"\">本地通信（</span></span><span style=\"border: 0px;font-family: inherit;font-size: 15px;font-style: inherit;font-weight: inherit;margin: 0px;outline: 0px;padding: 0px;vertical-align: baseline;overflow-wrap: break-word;box-sizing: border-box;color: rgb(230, 233, 237);\"><span leaf=\"\">localhost</span></span><span style=\"border: 0px;font-family: inherit;font-size: 15px;font-style: inherit;font-weight: inherit;margin: 0px;outline: 0px;padding: 0px;vertical-align: baseline;overflow-wrap: break-word;box-sizing: border-box;color: rgb(230, 233, 237);\"><span leaf=\"\">）</span></span></li><li style=\"border: 0px;font-family: inherit;font-size: 15px;font-style: inherit;font-weight: inherit;margin: 0px;outline: 0px;padding: 0px 0px 0px 0.5em;vertical-align: baseline;overflow-wrap: break-word;box-sizing: border-box;background-color: rgb(47, 54, 64);list-style-type: decimal;\"><span style=\"border: 0px;font-family: inherit;font-size: 15px;font-style: inherit;font-weight: inherit;margin: 0px;outline: 0px;padding: 0px;vertical-align: baseline;overflow-wrap: break-word;box-sizing: border-box;color: rgb(230, 233, 237);\"><span leaf=\"\">┌────────▼────────────┐</span></span></li><li style=\"border: 0px;font-family: inherit;font-size: 15px;font-style: inherit;font-weight: inherit;margin: 0px;outline: 0px;padding: 0px 0px 0px 0.5em;vertical-align: baseline;overflow-wrap: break-word;box-sizing: border-box;background-color: rgb(47, 54, 64);list-style-type: decimal;\"><span style=\"border: 0px;font-family: inherit;font-size: 15px;font-style: inherit;font-weight: inherit;margin: 0px;outline: 0px;padding: 0px;vertical-align: baseline;overflow-wrap: break-word;box-sizing: border-box;color: rgb(230, 233, 237);\"><span leaf=\"\">│</span></span><span style=\"border: 0px;font-family: inherit;font-size: 15px;font-style: inherit;font-weight: inherit;margin: 0px;outline: 0px;padding: 0px;vertical-align: baseline;overflow-wrap: break-word;box-sizing: border-box;color: rgb(230, 233, 237);\"></span><span style=\"border: 0px;font-family: inherit;font-size: 15px;font-style: inherit;font-weight: inherit;margin: 0px;outline: 0px;padding: 0px;vertical-align: baseline;overflow-wrap: break-word;box-sizing: border-box;color: rgb(79, 193, 233);\"><span leaf=\"\">Sidecar</span></span><span style=\"border: 0px;font-family: inherit;font-size: 15px;font-style: inherit;font-weight: inherit;margin: 0px;outline: 0px;padding: 0px;vertical-align: baseline;overflow-wrap: break-word;box-sizing: border-box;color: rgb(230, 233, 237);\"></span><span style=\"border: 0px;font-family: inherit;font-size: 15px;font-style: inherit;font-weight: inherit;margin: 0px;outline: 0px;padding: 0px;vertical-align: baseline;overflow-wrap: break-word;box-sizing: border-box;color: rgb(230, 233, 237);\"><span leaf=\"\">容器（如</span></span><span style=\"border: 0px;font-family: inherit;font-size: 15px;font-style: inherit;font-weight: inherit;margin: 0px;outline: 0px;padding: 0px;vertical-align: baseline;overflow-wrap: break-word;box-sizing: border-box;color: rgb(230, 233, 237);\"></span><span style=\"border: 0px;font-family: inherit;font-size: 15px;font-style: inherit;font-weight: inherit;margin: 0px;outline: 0px;padding: 0px;vertical-align: baseline;overflow-wrap: break-word;box-sizing: border-box;color: rgb(79, 193, 233);\"><span leaf=\"\">Envoy</span></span><span style=\"border: 0px;font-family: inherit;font-size: 15px;font-style: inherit;font-weight: inherit;margin: 0px;outline: 0px;padding: 0px;vertical-align: baseline;overflow-wrap: break-word;box-sizing: border-box;color: rgb(230, 233, 237);\"><span leaf=\"\">）</span></span><span style=\"border: 0px;font-family: inherit;font-size: 15px;font-style: inherit;font-weight: inherit;margin: 0px;outline: 0px;padding: 0px;vertical-align: baseline;overflow-wrap: break-word;box-sizing: border-box;color: rgb(230, 233, 237);\"></span><span style=\"border: 0px;font-family: inherit;font-size: 15px;font-style: inherit;font-weight: inherit;margin: 0px;outline: 0px;padding: 0px;vertical-align: baseline;overflow-wrap: break-word;box-sizing: border-box;color: rgb(230, 233, 237);\"><span leaf=\"\">│</span></span></li><li style=\"border: 0px;font-family: inherit;font-size: 15px;font-style: inherit;font-weight: inherit;margin: 0px;outline: 0px;padding: 0px 0px 0px 0.5em;vertical-align: baseline;overflow-wrap: break-word;box-sizing: border-box;background-color: rgb(47, 54, 64);list-style-type: decimal;\"><span style=\"border: 0px;font-family: inherit;font-size: 15px;font-style: inherit;font-weight: inherit;margin: 0px;outline: 0px;padding: 0px;vertical-align: baseline;overflow-wrap: break-word;box-sizing: border-box;color: rgb(230, 233, 237);\"><span leaf=\"\">└─────────────────────┘</span></span></li></ol></pre><p data-start=\"647\" data-end=\"672\" style=\"border: 0px;font-family: inherit;font-size: 16px;font-style: inherit;font-weight: inherit;margin: 0px 0px 1.7em;outline: 0px;padding: 0px;vertical-align: baseline;overflow-wrap: break-word;box-sizing: border-box;\"><span leaf=\"\">所有入站/出站流量，都由 Sidecar 拦截并处理。</span></p><p data-start=\"675\" data-end=\"717\" style=\"border: 0px;font-family: inherit;font-size: 16px;font-style: inherit;font-weight: inherit;margin: 0px 0px 1.7em;outline: 0px;padding: 0px;vertical-align: baseline;overflow-wrap: break-word;box-sizing: border-box;\"><span leaf=\"\">Sidecar 提供服务发现、负载均衡、熔断、限流、加密通信、日志收集等功能。</span></p><p style=\"border: 0px;font-family: -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Microsoft YaHei&quot;, &quot;Source Han Sans SC&quot;, &quot;Noto Sans CJK SC&quot;, &quot;WenQuanYi Micro Hei&quot;, sans-serif;font-size: 16px;font-style: normal;font-weight: 400;margin: 0px 0px 1.7em;outline: 0px;padding: 0px;vertical-align: baseline;overflow-wrap: break-word;box-sizing: border-box;word-break: normal;line-height: 1.5;color: rgb(34, 34, 34);font-variant-ligatures: normal;font-variant-caps: normal;letter-spacing: normal;orphans: 2;text-align: start;text-indent: 0px;text-transform: none;widows: 2;word-spacing: 0px;-webkit-text-stroke-width: 0px;white-space: normal;background-color: rgb(255, 255, 255);text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;\"><span leaf=\"\">&nbsp;</span></p><p data-track=\"10\" style=\"border: 0px;font-family: -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Microsoft YaHei&quot;, &quot;Source Han Sans SC&quot;, &quot;Noto Sans CJK SC&quot;, &quot;WenQuanYi Micro Hei&quot;, sans-serif;font-size: 16px;font-style: normal;font-weight: 400;margin: 0px 0px 1.7em;outline: 0px;padding: 0px;vertical-align: baseline;overflow-wrap: break-word;box-sizing: border-box;word-break: normal;line-height: 1.5;color: rgb(34, 34, 34);font-variant-ligatures: normal;font-variant-caps: normal;letter-spacing: normal;orphans: 2;text-align: start;text-indent: 0px;text-transform: none;widows: 2;word-spacing: 0px;-webkit-text-stroke-width: 0px;white-space: normal;background-color: rgb(255, 255, 255);text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;\"><strong style=\"border: 0px;font-family: inherit;font-size: 16px;font-style: inherit;font-weight: bold;margin: 0px;outline: 0px;padding: 0px;vertical-align: baseline;overflow-wrap: break-word;box-sizing: border-box;\"><span leaf=\"\">微服务异步消息设计</span></strong></p><p data-track=\"10\" style=\"border: 0px;font-family: -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Microsoft YaHei&quot;, &quot;Source Han Sans SC&quot;, &quot;Noto Sans CJK SC&quot;, &quot;WenQuanYi Micro Hei&quot;, sans-serif;font-size: 16px;font-style: normal;font-weight: 400;margin: 0px 0px 1.7em;outline: 0px;padding: 0px;vertical-align: baseline;overflow-wrap: break-word;box-sizing: border-box;word-break: normal;line-height: 1.5;color: rgb(34, 34, 34);font-variant-ligatures: normal;font-variant-caps: normal;letter-spacing: normal;orphans: 2;text-align: start;text-indent: 0px;text-transform: none;widows: 2;word-spacing: 0px;-webkit-text-stroke-width: 0px;white-space: normal;background-color: rgb(255, 255, 255);text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;\"><span leaf=\"\">异步消息模式，通过异步的方式，实现服务间的解耦。</span></p><p data-track=\"10\" style=\"border: 0px;font-family: -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Microsoft YaHei&quot;, &quot;Source Han Sans SC&quot;, &quot;Noto Sans CJK SC&quot;, &quot;WenQuanYi Micro Hei&quot;, sans-serif;font-size: 16px;font-style: normal;font-weight: 400;margin: 0px 0px 1.7em;outline: 0px;padding: 0px;vertical-align: baseline;overflow-wrap: break-word;box-sizing: border-box;word-break: normal;line-height: 1.5;color: rgb(34, 34, 34);font-variant-ligatures: normal;font-variant-caps: normal;letter-spacing: normal;orphans: 2;text-align: start;text-indent: 0px;text-transform: none;widows: 2;word-spacing: 0px;-webkit-text-stroke-width: 0px;white-space: normal;background-color: rgb(255, 255, 255);text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;\"><span leaf=\"\">比如：可以使用消息队列、或事件，在微服务之间实现松耦合通信。</span></p><p data-track=\"10\" style=\"border: 0px;font-family: -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Microsoft YaHei&quot;, &quot;Source Han Sans SC&quot;, &quot;Noto Sans CJK SC&quot;, &quot;WenQuanYi Micro Hei&quot;, sans-serif;font-size: 16px;font-style: normal;font-weight: 400;margin: 0px 0px 1.7em;outline: 0px;padding: 0px;vertical-align: baseline;overflow-wrap: break-word;box-sizing: border-box;word-break: normal;line-height: 1.5;color: rgb(34, 34, 34);font-variant-ligatures: normal;font-variant-caps: normal;letter-spacing: normal;orphans: 2;text-align: start;text-indent: 0px;text-transform: none;widows: 2;word-spacing: 0px;-webkit-text-stroke-width: 0px;white-space: normal;background-color: rgb(255, 255, 255);text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;\"><span leaf=\"\">比如：发送方（生产者）将消息发布到队列，而无需等待接收方（消费者）立即处理。</span></p><p data-track=\"10\" style=\"border: 0px;font-family: -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Microsoft YaHei&quot;, &quot;Source Han Sans SC&quot;, &quot;Noto Sans CJK SC&quot;, &quot;WenQuanYi Micro Hei&quot;, sans-serif;font-size: 16px;font-style: normal;font-weight: 400;margin: 0px 0px 1.7em;outline: 0px;padding: 0px;vertical-align: baseline;overflow-wrap: break-word;box-sizing: border-box;word-break: normal;line-height: 1.5;color: rgb(34, 34, 34);font-variant-ligatures: normal;font-variant-caps: normal;letter-spacing: normal;orphans: 2;text-align: start;text-indent: 0px;text-transform: none;widows: 2;word-spacing: 0px;-webkit-text-stroke-width: 0px;white-space: normal;background-color: rgb(255, 255, 255);text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;\"><span leaf=\"\">这种模式对于提高系统吞吐量、削峰填谷、处理复杂业务流程，以及实现最终一致性至关重要。</span></p><section nodeleaf=\"\"><img data-src=\"https://mmbiz.qpic.cn/sz_mmbiz_png/WH9ojuibmKSp8kECvD1hgxrEspd1HNSQBgfNb4Yiamz2GpuHe7UynTlbZIDj73ncz27uPtLaIGKaBrQAAkicrr68Q/640?wx_fmt=png&amp;from=appmsg\" alt=\"最新文章\" class=\"rich_pages wxw-img\" data-ratio=\"0.6334622823984526\" data-type=\"png\" data-w=\"1034\" height=\"488\" style=\"max-width: 100%;height: auto;object-fit: cover;border: 0px;vertical-align: text-top;opacity: 1;transition: opacity 400ms;cursor: zoom-in;\" width=\"770\" data-imgfileid=\"100006556\"></section><p><span leaf=\"\">服务之间的交互不再是同步调用，而是通过异步消息传递信息，从而提高系统的扩展性、和性能。</span></p><p style=\"border: 0px;font-family: -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Microsoft YaHei&quot;, &quot;Source Han Sans SC&quot;, &quot;Noto Sans CJK SC&quot;, &quot;WenQuanYi Micro Hei&quot;, sans-serif;font-size: 16px;font-style: normal;font-weight: 400;margin: 0px 0px 1.7em;outline: 0px;padding: 0px;vertical-align: baseline;overflow-wrap: break-word;box-sizing: border-box;word-break: normal;line-height: 1.5;color: rgb(34, 34, 34);font-variant-ligatures: normal;font-variant-caps: normal;letter-spacing: normal;orphans: 2;text-align: start;text-indent: 0px;text-transform: none;widows: 2;word-spacing: 0px;-webkit-text-stroke-width: 0px;white-space: normal;background-color: rgb(255, 255, 255);text-decoration-thickness: initial;text-decoration-style: initial;text-decoration-color: initial;\"><span leaf=\"\">适用于：解耦服务调用、实现弹性伸缩、提高系统吞吐（如：下单后通知多个系统）。</span></p><p style=\"margin-bottom: 0px;letter-spacing: 0.578px;\"><span style=\"font-size: 16px;\"><span leaf=\"\"><br></span></span></p><p style=\"margin-bottom: 0px;letter-spacing: 0.578px;\"><span style=\"font-size: 16px;\"><span leaf=\"\">以上</span></span></p><p style=\"margin-bottom: 0px;letter-spacing: 0.578px;\"><span leaf=\"\"><br></span></p><p style=\"margin-bottom: 0px;letter-spacing: 0.578px;\"><span style=\"letter-spacing: 0.034em;font-size: 16px;\"><span leaf=\"\">送我原创</span><strong><span leaf=\"\"><a class=\"normal_text_link\" target=\"_blank\" style=\"\" href=\"https://mp.weixin.qq.com/s?__biz=Mzg2NTg1NTQ2NQ==&amp;mid=2247500275&amp;idx=1&amp;sn=3c4d7ae5f524e012f2fb44fd2259ff4d&amp;chksm=ce513375f926ba63802e679bd2286b385f3709b62fadda6a1c3337d8dfc08e7e6eeb377a4156&amp;scene=21&amp;token=1498050498&amp;lang=zh_CN#wechat_redirect\" textvalue=\"30万字阿里架构师进阶从0到1合集\" linktype=\"text\" data-linktype=\"2\">30万字阿里架构师进阶从0到1合集</a></span></strong><strong style=\"margin: 0px;padding: 0px;max-width: 100%;box-sizing: border-box !important;overflow-wrap: break-word !important;\"><span leaf=\"\">。</span></strong></span><span style=\"letter-spacing: 0.034em;font-size: 16px;\"></span></p><p style=\"margin-bottom: 0px;letter-spacing: 0.578px;\"><span style=\"letter-spacing: 0.034em;font-size: 16px;\"><a href=\"http://mp.weixin.qq.com/s?__biz=Mzg2NTg1NTQ2NQ==&amp;mid=2247500275&amp;idx=1&amp;sn=3c4d7ae5f524e012f2fb44fd2259ff4d&amp;chksm=ce513375f926ba63802e679bd2286b385f3709b62fadda6a1c3337d8dfc08e7e6eeb377a4156&amp;scene=21#wechat_redirect\" imgurl=\"https://mmbiz.qpic.cn/sz_mmbiz_png/WH9ojuibmKSp7VqOGPKZryGY9qwnEiaibLS5526ZTlbxCvvNj0auiaMhFticibAsbOjFicVKr9NGzdAWP4lLOs8IBdIDg/640?wx_fmt=png&amp;from=appmsg\" linktype=\"image\" data-itemshowtype=\"0\" target=\"_blank\" data-linktype=\"1\"><span class=\"js_jump_icon h5_image_link\"><img data-src=\"https://mmbiz.qpic.cn/sz_mmbiz_png/WH9ojuibmKSp7VqOGPKZryGY9qwnEiaibLS5526ZTlbxCvvNj0auiaMhFticibAsbOjFicVKr9NGzdAWP4lLOs8IBdIDg/640?wx_fmt=png&amp;from=appmsg\" class=\"rich_pages wxw-img\" data-ratio=\"0.5879629629629629\" data-s=\"300,640\" data-type=\"png\" data-w=\"1080\" style=\"height: 340px;width: 578px;\" data-cropselx1=\"0\" data-cropselx2=\"578\" data-cropsely1=\"0\" data-cropsely2=\"344\" data-imgfileid=\"100005214\"></span></a></span><span style=\"letter-spacing: 0.578px;font-size: 16px;\"><span style=\"color: rgb(18, 18, 18);font-family: font-regular, &quot;Helvetica Neue&quot;, sans-serif;letter-spacing: normal;text-align: start;background-color: rgb(255, 255, 255);\"><span leaf=\"\"><br></span></span></span></p><p style=\"margin-bottom: 0px;letter-spacing: 0.578px;\"><span style=\"letter-spacing: 0.578px;font-size: 16px;\"><span style=\"color: rgb(18, 18, 18);font-family: font-regular, &quot;Helvetica Neue&quot;, sans-serif;letter-spacing: normal;text-align: start;background-color: rgb(255, 255, 255);\"><span leaf=\"\"><br></span></span></span></p><p style=\"margin-bottom: 0px;letter-spacing: 0.578px;\"><span style=\"letter-spacing: 0.578px;font-size: 16px;\"><span style=\"color: rgb(18, 18, 18);font-family: font-regular, &quot;Helvetica Neue&quot;, sans-serif;letter-spacing: normal;text-align: start;background-color: rgb(255, 255, 255);\"><span leaf=\"\">需要以上</span></span><strong style=\"outline: 0px;color: rgb(18, 18, 18);font-family: font-regular, &quot;Helvetica Neue&quot;, sans-serif;font-size: 16px;letter-spacing: normal;text-align: start;\"><span leaf=\"\">架构资料</span></strong><span style=\"color: rgb(18, 18, 18);font-family: font-regular, &quot;Helvetica Neue&quot;, sans-serif;letter-spacing: normal;text-align: start;background-color: rgb(255, 255, 255);\"><span leaf=\"\">的同学，加我微信即可领取！</span></span></span></p><p style=\"margin-bottom: 0px;letter-spacing: 0.578px;\"><span style=\"color: rgb(18, 18, 18);font-family: font-regular, &quot;Helvetica Neue&quot;, sans-serif;text-align: start;background-color: rgb(255, 255, 255);letter-spacing: 0.578px;font-size: 16px;\"><span leaf=\"\"><br></span></span></p><p style=\"margin-bottom: 0px;letter-spacing: 0.578px;\"><span style=\"font-size: 16px;\"><span leaf=\"\">添加时备注：</span><strong><span style=\"font-size: 16px;color: rgb(255, 0, 0);\"><span leaf=\"\">资料</span></span></strong></span></p><p style=\"margin-bottom: 0px;letter-spacing: 0.578px;text-align: center;\"><span leaf=\"\"><img data-src=\"https://mmbiz.qpic.cn/mmbiz_png/222fVT9YtibdzicXNbASxZWBWKicFeb3enbkUzMibBpiaUL0LZl8n0zZDIPGmKDXRSs0q3AibZnC8RFxXTicjqdKOkg7g/640?wx_fmt=png\" class=\"rich_pages wxw-img\" data-ratio=\"1\" data-s=\"300,640\" data-type=\"png\" data-w=\"400\" style=\"height: 259px;width: 259px;\" data-imgfileid=\"100005212\"></span></p><section><span leaf=\"\"><br></span></section><p style=\"display: none;\"><mp-style-type data-value=\"3\"></mp-style-type></p>", "msg_author": "mike<PERSON>", "msg_sn": "f0dabf0527c284088e28426518dc0d80", "msg_idx": 1, "msg_mid": 2247490210, "msg_title": "微服务架构模式最全详解(4大主流模式)", "msg_desc": "微服务架构设计模式是大型架构的核心，下面我重点详解微服务架构设计模式@mikechen", "msg_link": "https://mp.weixin.qq.com/s/3st7Ss9IGr0s_L1_9liDNQ", "msg_source_url": null, "msg_cover": "https://mmbiz.qpic.cn/sz_mmbiz_jpg/WH9ojuibmKSp8kECvD1hgxrEspd1HNSQBpiaTI2g19XteCqmeaHiakPE4ZwIfwmH8DeIAF5KRudqfAIqxEffibWQaQ/0?wx_fmt=jpeg", "msg_article_type": null, "msg_publish_time": "2025-06-19T07:59:08.000Z", "msg_publish_time_str": "2025/06/19 15:59:08", "msg_type": "post"}}