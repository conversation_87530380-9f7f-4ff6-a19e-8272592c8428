{"code": 0, "done": true, "data": {"account_name": "悟空聊架构", "account_alias": null, "account_avatar": "http://wx.qlogo.cn/mmhead/Q3auHgzwzM7eiboMxSqCG7MSWaTmQlugatnlbsiaSsqPwlsxpDJSFmJg/132", "account_description": "用故事讲解分布式、架构。 《 JVM 性能调优实战》专栏作者， 《Spring Cloud 实战 PassJava》开源作者， 自主开发了 PMP 刷题小程序。", "account_id": "gh_33b54c2a2622", "account_biz": "MzAwMjI0ODk0NA==", "account_biz_number": **********, "account_qr_code": "https://open.weixin.qq.com/qr/code?username=gh_33b54c2a2622", "msg_has_copyright": false, "msg_content": "<p data-first-child=\"\" data-pid=\"ycFbDMxO\" style=\"margin: 0px 0px 1.4em;\" data-pm-slice=\"0 0 []\"><b style=\"font-weight: 600;\"><span leaf=\"\">AI大模型技术全景视图：</span></b></p><figure data-size=\"normal\" style=\"margin: 1.4em 0px;\"><section nodeleaf=\"\"><img data-src=\"https://mmbiz.qpic.cn/sz_mmbiz_jpg/UHKG18j8iasbvcYL4nNv2kwDa5te76H9eXDoMafXv1icffwrficyaawDGwEWWuTUpYfsjreCP3YGhMb9qBJ5a3Wwg/640?wx_fmt=jpeg&amp;from=appmsg\" class=\"rich_pages wxw-img js_img_placeholder wx_img_placeholder\" data-ratio=\"0.****************\" data-type=\"jpeg\" data-w=\"1080\" style=\"display: block;margin: 0px auto;max-width: 100%;height: auto;background-color: rgb(255, 255, 255);cursor: zoom-in;width: 654px;\" width=\"1080\" data-imgfileid=\"*********\"></section></figure><p data-pid=\"LTdzUZEG\" style=\"margin: 1.4em 0px;\"><b style=\"font-weight: 600;\"><span leaf=\"\">AI大模型通用技术架构图</span></b></p><figure data-size=\"normal\" style=\"margin: 1.4em 0px;\"><section nodeleaf=\"\"><img data-src=\"https://mmbiz.qpic.cn/sz_mmbiz_jpg/UHKG18j8iasbvcYL4nNv2kwDa5te76H9eeqibtXo5W3dLebBUELXJygysmVOFjxJG1SkJT2B0qWC3lhIwFrN89sg/640?wx_fmt=jpeg&amp;from=appmsg\" class=\"rich_pages wxw-img js_img_placeholder wx_img_placeholder\" data-ratio=\"0.7018518518518518\" data-type=\"jpeg\" data-w=\"1080\" style=\"display: block;margin: 0px auto;max-width: 100%;height: auto;background-color: rgb(255, 255, 255);cursor: zoom-in;width: 654px;\" width=\"1080\" data-imgfileid=\"100011609\"></section></figure><p data-pid=\"qRKtHo0U\" style=\"margin: 1.4em 0px;\"><b style=\"font-weight: 600;\"><span leaf=\"\">AI大模型通用技术架构图</span></b></p><figure data-size=\"normal\" style=\"margin: 1.4em 0px;\"><section nodeleaf=\"\"><img data-src=\"https://mmbiz.qpic.cn/sz_mmbiz_jpg/UHKG18j8iasbvcYL4nNv2kwDa5te76H9eb192L2DiacMqBRCWnRZ5buZicAFsdf8LiakWQDHjJ4J4HgMicsGfOvHoCQ/640?wx_fmt=jpeg&amp;from=appmsg\" class=\"rich_pages wxw-img js_img_placeholder wx_img_placeholder\" data-ratio=\"0.8407407407407408\" data-type=\"jpeg\" data-w=\"1080\" style=\"display: block;margin: 0px auto;max-width: 100%;height: auto;background-color: rgb(255, 255, 255);cursor: zoom-in;width: 654px;\" width=\"1080\" data-imgfileid=\"100011607\"></section></figure><p data-pid=\"gfmFI1Tt\" style=\"margin: 1.4em 0px;\"><b style=\"font-weight: 600;\"><span leaf=\"\">AI大模型通用技术架构图</span></b></p><figure data-size=\"normal\" style=\"margin: 1.4em 0px;\"><section nodeleaf=\"\"><img data-src=\"https://mmbiz.qpic.cn/sz_mmbiz_jpg/UHKG18j8iasbvcYL4nNv2kwDa5te76H9eE8S7Zfq1yTW0wBUWoZzErCRhicVQDy26xMUejq2kutk4rQmw4T9Z5LA/640?wx_fmt=jpeg&amp;from=appmsg\" class=\"rich_pages wxw-img js_img_placeholder wx_img_placeholder\" data-ratio=\"0.7842592592592592\" data-type=\"jpeg\" data-w=\"1080\" style=\"display: block;margin: 0px auto;max-width: 100%;height: auto;background-color: rgb(255, 255, 255);cursor: zoom-in;width: 654px;\" width=\"1080\" data-imgfileid=\"100011606\"></section></figure><p data-pid=\"E3rxBDL3\" style=\"margin: 1.4em 0px;\"><b style=\"font-weight: 600;\"><span leaf=\"\">RAG知识库业务架构图</span></b></p><figure data-size=\"normal\" style=\"margin: 1.4em 0px;\"><section nodeleaf=\"\"><img data-src=\"https://mmbiz.qpic.cn/sz_mmbiz_jpg/UHKG18j8iasbvcYL4nNv2kwDa5te76H9e47rpYfuMAqmQldmiad3iaDFfhJXHV1AibibEVCNjPyT7Ig44ibuKPWgZ6Ew/640?wx_fmt=jpeg&amp;from=appmsg\" class=\"rich_pages wxw-img js_img_placeholder wx_img_placeholder\" data-ratio=\"0.5675925925925925\" data-type=\"jpeg\" data-w=\"1080\" style=\"display: block;margin: 0px auto;max-width: 100%;height: auto;background-color: rgb(255, 255, 255);cursor: zoom-in;width: 654px;\" width=\"1080\" data-imgfileid=\"100011605\"></section></figure><p data-pid=\"SDVBwPqc\" style=\"margin: 1.4em 0px;\"><b style=\"font-weight: 600;\"><span leaf=\"\">AI农业大模型技术架构图</span></b></p><figure data-size=\"normal\" style=\"margin: 1.4em 0px;\"><section nodeleaf=\"\"><img data-src=\"https://mmbiz.qpic.cn/sz_mmbiz_jpg/UHKG18j8iasbvcYL4nNv2kwDa5te76H9ebnGgvQXHqAKBAXJB2jicHD5FVXotzZwTNhAqwpCCHdhWnMxZOmyDiahw/640?wx_fmt=jpeg&amp;from=appmsg\" class=\"rich_pages wxw-img js_img_placeholder wx_img_placeholder\" data-ratio=\"0.8453703703703703\" data-type=\"jpeg\" data-w=\"1080\" style=\"display: block;margin: 0px auto;max-width: 100%;height: auto;background-color: rgb(255, 255, 255);cursor: zoom-in;width: 654px;\" width=\"1080\" data-imgfileid=\"100011614\"></section></figure><p data-pid=\"Jg6ub16q\" style=\"margin: 1.4em 0px;\"><b style=\"font-weight: 600;\"><span leaf=\"\">AI导购大模型技术架构图</span></b></p><figure data-size=\"normal\" style=\"margin: 1.4em 0px;\"><section nodeleaf=\"\"><img data-src=\"https://mmbiz.qpic.cn/sz_mmbiz_jpg/UHKG18j8iasbvcYL4nNv2kwDa5te76H9eTEukWFHJcJOSxTlE4iaOfMMTUqiawh4SLU0jqArboSJXCLAeic4XlLaFg/640?wx_fmt=jpeg&amp;from=appmsg\" class=\"rich_pages wxw-img js_img_placeholder wx_img_placeholder\" data-ratio=\"0.7694444444444445\" data-type=\"jpeg\" data-w=\"1080\" style=\"display: block;margin: 0px auto;max-width: 100%;height: auto;background-color: rgb(255, 255, 255);cursor: zoom-in;width: 654px;\" width=\"1080\" data-imgfileid=\"100011612\"></section></figure><p data-pid=\"VCOTOVFc\" style=\"margin: 1.4em 0px;\"><b style=\"font-weight: 600;\"><span leaf=\"\">AI大模型物联网AloT架构图</span></b></p><figure data-size=\"normal\" style=\"margin: 1.4em 0px;\"><section nodeleaf=\"\"><img data-src=\"https://mmbiz.qpic.cn/sz_mmbiz_jpg/UHKG18j8iasbvcYL4nNv2kwDa5te76H9eoWLQ0TmCM29y1Lw0qhAuu4IBWOibDGT6K1zpFKvcsMnt7Vq3W6uhRDg/640?wx_fmt=jpeg&amp;from=appmsg\" class=\"rich_pages wxw-img js_img_placeholder wx_img_placeholder\" data-ratio=\"0.9124293785310734\" data-type=\"jpeg\" data-w=\"1062\" style=\"display: block;margin: 0px auto;max-width: 100%;height: auto;background-color: rgb(255, 255, 255);cursor: zoom-in;width: 654px;\" width=\"1062\" data-imgfileid=\"100011613\"></section></figure><p data-pid=\"bn1CCDy6\" style=\"margin: 1.4em 0px;\"><b style=\"font-weight: 600;\"><span leaf=\"\">AI大模型合规风控管理架构图</span></b></p><figure data-size=\"normal\" style=\"margin: 1.4em 0px;\"><section nodeleaf=\"\"><img data-src=\"https://mmbiz.qpic.cn/sz_mmbiz_jpg/UHKG18j8iasbvcYL4nNv2kwDa5te76H9ecxtLkc9CX9TS4zbnT642icRTLUqfo3vOBOyUk0Mnym5h7MFdTgicG4mg/640?wx_fmt=jpeg&amp;from=appmsg\" class=\"rich_pages wxw-img js_img_placeholder wx_img_placeholder\" data-ratio=\"0.6472222222222223\" data-type=\"jpeg\" data-w=\"1080\" style=\"display: block;margin: 0px auto;max-width: 100%;height: auto;background-color: rgb(255, 255, 255);cursor: zoom-in;width: 654px;\" width=\"1080\" data-imgfileid=\"100011611\"></section></figure><p data-pid=\"qapiyp_a\" style=\"margin: 1.4em 0px;\"><b style=\"font-weight: 600;\"><span leaf=\"\">AI大模型合规管理架构图</span></b></p><figure data-size=\"normal\" style=\"margin: 1.4em 0px;\"><section nodeleaf=\"\"><img data-src=\"https://mmbiz.qpic.cn/sz_mmbiz_jpg/UHKG18j8iasbvcYL4nNv2kwDa5te76H9esTPOL6JjWT9wpJt88jZYXNu68FNHhPIb4h6icZFUOva9tPRfMalS1Aw/640?wx_fmt=jpeg&amp;from=appmsg\" class=\"rich_pages wxw-img js_img_placeholder wx_img_placeholder\" data-ratio=\"0.3314814814814815\" data-type=\"jpeg\" data-w=\"1080\" style=\"display: block;margin: 0px auto;max-width: 100%;height: auto;background-color: rgb(255, 255, 255);cursor: zoom-in;width: 654px;\" width=\"1080\" data-imgfileid=\"100011610\"></section></figure><p data-pid=\"4Mom-Qvc\" style=\"margin: 1.4em 0px;\"><b style=\"font-weight: 600;\"><span leaf=\"\">AI大模型Agent平台架构图</span></b></p><figure data-size=\"normal\" style=\"margin: 1.4em 0px;\"><section nodeleaf=\"\"><img data-src=\"https://mmbiz.qpic.cn/sz_mmbiz_jpg/UHKG18j8iasbvcYL4nNv2kwDa5te76H9ep00Lz06jh3sf1ovX9eshVFSo2oInsbH4nJsicokk62mRNibPqia8roslw/640?wx_fmt=jpeg&amp;from=appmsg\" class=\"rich_pages wxw-img js_img_placeholder wx_img_placeholder\" data-ratio=\"0.712037037037037\" data-type=\"jpeg\" data-w=\"1080\" style=\"display: block;margin: 0px auto;max-width: 100%;height: auto;background-color: rgb(255, 255, 255);cursor: zoom-in;width: 654px;\" width=\"1080\" data-imgfileid=\"100011617\"></section></figure><p data-pid=\"YGlgJdZu\" style=\"margin: 1.4em 0px;\"><b style=\"font-weight: 600;\"><span leaf=\"\">AI大模型+CRM架构图</span></b></p><figure data-size=\"normal\" style=\"margin: 1.4em 0px;\"><section nodeleaf=\"\"><img data-src=\"https://mmbiz.qpic.cn/sz_mmbiz_jpg/UHKG18j8iasbvcYL4nNv2kwDa5te76H9ebudw0gibuDibTHgLjKTVooA2KcmQouvszic8ekHZpXw9babrgIrIHPohw/640?wx_fmt=jpeg&amp;from=appmsg\" class=\"rich_pages wxw-img js_img_placeholder wx_img_placeholder\" data-ratio=\"0.5638888888888889\" data-type=\"jpeg\" data-w=\"1080\" style=\"display: block;margin: 0px auto;max-width: 100%;height: auto;background-color: rgb(255, 255, 255);cursor: zoom-in;width: 654px;\" width=\"1080\" data-imgfileid=\"100011616\"></section></figure><p data-pid=\"RfWQoLwr\" style=\"margin: 1.4em 0px;\"><b style=\"font-weight: 600;\"><span leaf=\"\">AI导购大模型架构图</span></b></p><figure data-size=\"normal\" style=\"margin: 1.4em 0px;\"><section nodeleaf=\"\"><img data-src=\"https://mmbiz.qpic.cn/sz_mmbiz_jpg/UHKG18j8iasbvcYL4nNv2kwDa5te76H9eTEukWFHJcJOSxTlE4iaOfMMTUqiawh4SLU0jqArboSJXCLAeic4XlLaFg/640?wx_fmt=jpeg&amp;from=appmsg\" class=\"rich_pages wxw-img js_img_placeholder wx_img_placeholder\" data-ratio=\"0.7694444444444445\" data-type=\"jpeg\" data-w=\"1080\" style=\"display: block;margin: 0px auto;max-width: 100%;height: auto;background-color: rgb(255, 255, 255);cursor: zoom-in;width: 654px;\" width=\"1080\" data-imgfileid=\"100011615\"></section></figure><section style=\"margin-bottom: 0px;\"><span leaf=\"\"><span textstyle=\"\" style=\"font-size: 12px;\">出处：https://zhuanlan.zhihu.com/p/1898655667440575331</span></span></section><p style=\"display: none;\"><mp-style-type data-value=\"3\"></mp-style-type></p>", "msg_sn": "ef62f84e436f3029e36958e729bf6d7a", "msg_idx": 1, "msg_mid": 2451972707, "msg_title": "AI大模型应用架构图大全", "msg_desc": "AI大模型应用架构图大全", "msg_link": "https://mp.weixin.qq.com/s/Dbz_fajqRvzeTl2dmNdWUw", "msg_source_url": null, "msg_cover": "https://mmbiz.qpic.cn/mmbiz_jpg/SfAHMuUxqJ2cxRhXtzrNQG8IumIMZUK82iaeyQxibFfxu5NuPVX25QlKnwkE5HibicXCMMIBGP4icFoeh6UfWohaZJQ/0?wx_fmt=jpeg", "msg_article_type": null, "msg_publish_time": "2025-06-27T00:30:17.000Z", "msg_publish_time_str": "2025/06/27 08:30:17", "msg_type": "post"}}